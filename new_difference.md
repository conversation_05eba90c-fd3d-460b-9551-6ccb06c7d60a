# Comparison Results: New Dataset vs New Output

**Summary:**
- Expected records: 150
- Actual records: 150
- Mismatched records: 46
- Total field differences: 68

## Mismatched Records

### Record 1

**Input:** `Bacnet-DGEORGE_ST_101_ROOF_1234 1234:VAV_L11_N_1/EFFECT_FLOW_SP`

**Different fields:** equipName

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | vav | flowsp | VAV L11-N1 | Level 11 |
| Actual | vav | flowsp | VAV L11-N-1 | Level 11 |

---

### Record 2

**Input:** `Bacnet-DGEORGE_ST_101_ROOF_1234 1234:PULSE_2/L_8_HW_1`

**Different fields:** level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | wm | volumewater | WM L8-HW1 | Level Submeter |
| Actual | wm | volumewater | WM L8-HW1 | Level 8 |

---

### Record 3

**Input:** `Drivers.ModbusAsyncNetwork1.Pulse2.points.L8_HW1`

**Different fields:** level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | wm | volumewater | WM L8-HW1 | Level Submeter |
| Actual | wm | volumewater | WM L8-HW1 | Level 8 |

---

### Record 4

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/BUILDING/PULSE_2/L_8_HW_1`

**Different fields:** level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | wm | volumewater | WM L8-HW1 | Level Submeter |
| Actual | wm | volumewater | WM L8-HW1 | Level 8 |

---

### Record 5

**Input:** `Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:FCU_L8_01/ZONE_1_TEMP`

**Different fields:** gegEquipType, equipName

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | fcu | zt | FCU L8-1 | Level 8 |
| Actual | pac | zt | PAC L8-1 | Level 8 |

---

### Record 6

**Input:** `Bacnet-DGEORGE_ST_101_ROOF_1234 1234:TCWP_3/KWH_NR`

**Different fields:** gegEquipType, gegPointType, equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pm | totalenergyactiveimport | PM TCWP-3 | Level Submeter |
| Actual | tcwp | fault | TCWP 3-1234 | Output: Level 3 |

---

### Record 7

**Input:** `Drivers.BacnetNetwork.PLANTROOM_VSDS.TCWP_3.points.KWH_NR`

**Different fields:** gegEquipType, gegPointType, equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pm | totalenergyactiveimport | PM TCWP-3 | Level Submeter |
| Actual | tcwp | fault | TCWP PLANTROOM-3 | Level 3 |

---

### Record 8

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/PLANTROOM/VSD/TCWP_3/KWH_NR`

**Different fields:** gegEquipType, gegPointType, equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pm | totalenergyactiveimport | PM TCWP-3 | Level Submeter |
| Actual | tcwp | fault | TCWP PLANTROOM-3 | Level 3 |

---

### Record 9

**Input:** `Drivers.BacnetNetwork.LEVEL_9.PAC_L9_01.points.StsIn`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | status | PAC L9-1 | Level 9 |
| Actual | pac | unknown | PAC L9-1 | Level 9 |

---

### Record 10

**Input:** `Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:PAC_L4_02/FILTER_STATUS`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | fault | PAC L4-2 | Level 4 |
| Actual | pac | filterstatus | PAC L4-2 | Level 4 |

---

### Record 11

**Input:** `Drivers.BacnetNetwork.LEVEL_4.PAC_L4_02.points.FilterSts`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | fault | PAC L4-2 | Level 4 |
| Actual | pac | filterstatus | PAC L4-2 | Level 4 |

---

### Record 12

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_04/HVAC/PAC_L4_02/FILTER_STATUS`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | fault | PAC L4-2 | Level 4 |
| Actual | pac | filterstatus | PAC L4-2 | Level 4 |

---

### Record 13

**Input:** `Drivers.BacnetNetwork.HitahiVRV.HITACHI_PAC_GATEWAY.points.FCU_L9_01.FCU_09_01_Indoor_Temperature_Sp`

**Different fields:** equipName

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | fcu | ztsp | FCU L9-1 | Level 9 |
| Actual | fcu | ztsp | FCU L9-9 | Level 9 |

---

### Record 14

**Input:** `Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:COMMS_ROOM/PAC_STATUS`

**Different fields:** equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | status | PAC L3-Comms Room | Level 3 |
| Actual | pac | status | PAC L4-5678 | Level 4 |

---

### Record 15

**Input:** `Drivers.BacnetNetwork.LEVEL_3.CommsRm.PAC_Sts`

**Different fields:** gegPointType, equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | status | PAC L3-Comms Room | Level 3 |
| Actual | pac | unknown | PAC COMMSRM | Level CommsRm |

---

### Record 16

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_03/HVAC/COMMS_ROOM/PAC_STATUS`

**Different fields:** equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | status | PAC L3-Comms Room | Level 3 |
| Actual | pac | status | PAC COMMS_ROOM | Level COMMS_ROOM |

---

### Record 17

**Input:** `Bacnet-DGEORGE_ST_101_GF_4567 4567:FANS_GF/RETAIL_SA_FAN_1_CMD`

**Different fields:** gegEquipType, equipName

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | ksaf | startstop | KSAF Retail - 1 | Level Ground |
| Actual | fcu | startstop | FAN GF-1 | Level Ground |

---

### Record 18

**Input:** `Drivers.BacnetNetwork.GF_CONTROLERS.FANS_GF.points.RetailSaFan_1_Cmd`

**Different fields:** gegEquipType, equipName

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | ksaf | startstop | KSAF Retail - 1 | Level Ground |
| Actual | saf | startstop | SAF GF-1 | Level Ground |

---

### Record 19

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_GF/HVAC/FANS_GF/RETAIL_SA_FAN_1_CMD`

**Different fields:** gegEquipType, equipName

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | ksaf | startstop | KSAF Retail - 1 | Level Ground |
| Actual | saf | startstop | SAF LEVEL_GF-RETAIL_1 | Level Ground |

---

### Record 20

**Input:** `Bacnet-DGEORGE_ST_101_L8_2345 2345:VAV_L7_C_10/OCC_MODE`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | vav | "" | VAV L7-C10 | Level 7 |
| Actual | vav | occupancy | VAV L7-C10 | Level 7 |

---

### Record 21

**Input:** `Drivers.BacnetNetwork.L7_VAVS.VAV_L7_C10.points.OCC_Mode`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | vav | "" | VAV L7-C10 | Level 7 |
| Actual | vav | occupancy | VAV L7-C10 | Level 7 |

---

### Record 22

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_07/HVAC/VAV_L7_C_10/OCC_MODE`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | vav | "" | VAV L7-C10 | Level 7 |
| Actual | vav | occupancy | VAV L7-C10 | Level 7 |

---

### Record 23

**Input:** `Bacnet-DGEORGE_ST_101_L4_3456 3456:VAV_L4_C_7/OCC_MODE`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | vav | "" | VAV L4-C7 | Level 4 |
| Actual | vav | occupancy | VAV L4-C7 | Level 4 |

---

### Record 24

**Input:** `Drivers.BacnetNetwork.L4_VAVS.VAV_L4_C7.points.OCC_Mode`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | vav | "" | VAV L4-C7 | Level 4 |
| Actual | vav | occupancy | VAV L4-C7 | Level 4 |

---

### Record 25

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_04/HVAC/VAV_L4_C_7/OCC_MODE`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | vav | "" | VAV L4-C7 | Level 4 |
| Actual | vav | occupancy | VAV L4-C7 | Level 4 |

---

### Record 26

**Input:** `Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:PAC_L8_06/FILTER_STATUS`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | fault | PAC L8-6 | Level 8 |
| Actual | pac | filterstatus | PAC L8-6 | Level 8 |

---

### Record 27

**Input:** `Drivers.BacnetNetwork.LEVEL_8.PAC_L8_06.points.FilterSts`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | fault | PAC L8-6 | Level 8 |
| Actual | pac | filterstatus | PAC L8-6 | Level 8 |

---

### Record 28

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_08/HVAC/PAC_L8_06/FILTER_STATUS`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | fault | PAC L8-6 | Level 8 |
| Actual | pac | filterstatus | PAC L8-6 | Level 8 |

---

### Record 29

**Input:** `Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:PAC_L5_07/FILTER_STATUS`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | fault | PAC L5-7 | Level 5 |
| Actual | pac | filterstatus | PAC L5-7 | Level 5 |

---

### Record 30

**Input:** `Drivers.BacnetNetwork.LEVEL_5.PAC_L5_07.points.FilterSts`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | fault | PAC L5-7 | Level 5 |
| Actual | pac | filterstatus | PAC L5-7 | Level 5 |

---

### Record 31

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_05/HVAC/PAC_L5_07/FILTER_STATUS`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | fault | PAC L5-7 | Level 5 |
| Actual | pac | filterstatus | PAC L5-7 | Level 5 |

---

### Record 32

**Input:** `Bacnet-DGEORGE_ST_101_ROOF_1234 1234:AHU_5_6_7/AHU_5/CHW_VALVE`

**Different fields:** gegPointType, equipName

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | ahu | chwv | AHU 5 | Level Roof |
| Actual | ahu | satsp | AHU 5-6-7 | Level Roof |

---

### Record 33

**Input:** `Drivers.BacnetNetwork.PLANTROOM.AHU_5_6_7.points.AHU_5_ChwVlvl`

**Different fields:** equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | ahu | chwv | AHU 5 | Level Roof |
| Actual | ahu | chwv | AHU PLANTROOM-5_6_7 | Level Plantroom |

---

### Record 34

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/PLANTROOM/HVAC/AHU_5_6_7/AHU_5/CHW_VALVE`

**Different fields:** level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | ahu | chwv | AHU 5 | Level Roof |
| Actual | ahu | chwv | AHU 5 | Output: Level 5 |

---

### Record 35

**Input:** `Bacnet-DGEORGE_ST_101_ROOF_1234 1234:PULSE_2/L_10_CW_1`

**Different fields:** level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | wm | volumewater | WM L10-CW1 | Level Submeter |
| Actual | wm | volumewater | WM L10-CW1 | Level 10 |

---

### Record 36

**Input:** `Drivers.ModbusAsyncNetwork1.Pulse2.points.L10_CW1`

**Different fields:** level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | wm | volumewater | WM L10-CW1 | Level Submeter |
| Actual | wm | volumewater | WM L10-CW1 | Level 10 |

---

### Record 37

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/BUILDING/PULSE_2/L_10_CW_1`

**Different fields:** level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | wm | volumewater | WM L10-CW1 | Level Submeter |
| Actual | wm | volumewater | WM L10-CW1 | Level 10 |

---

### Record 38

**Input:** `Bacnet-DGEORGE_ST_101_ROOF_1234 1234:PAC_L11_01/COMP_CMD`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | compressorstartstop | PAC L11-1 | Level 11 |
| Actual | pac | unknown | PAC L11-1 | Level 11 |

---

### Record 39

**Input:** `Drivers.BacnetNetwork.L11_VAVS.PAC_L11_01.points.CompCmd`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | compressorstartstop | PAC L11-1 | Level 11 |
| Actual | pac | unknown | PAC L11-1 | Level 11 |

---

### Record 40

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_11/HVAC/PAC_L11_01/COMP_CMD`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | compressorstartstop | PAC L11-1 | Level 11 |
| Actual | pac | unknown | PAC L11-1 | Level 11 |

---

### Record 41

**Input:** `Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:PAC_L6_02/COMP_CMD`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | compressorstartstop | PAC L6-2 | Level 6 |
| Actual | pac | startstop | PAC L6-2 | Level 6 |

---

### Record 42

**Input:** `Drivers.BacnetNetwork.LEVEL_6.PAC_L6_02.points.CompCmd`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | compressorstartstop | PAC L6-2 | Level 6 |
| Actual | pac | unknown | PAC L6-2 | Level 6 |

---

### Record 43

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_06/HVAC/PAC_L6_02/COMP_CMD`

**Different fields:** gegPointType

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | pac | compressorstartstop | PAC L6-2 | Level 6 |
| Actual | pac | startstop | PAC L6-2 | Level 6 |

---

### Record 44

**Input:** `Bacnet-DGEORGE_ST_101_ROOF_1234 1234:AHU_3_4/SOUTH_RA_TEMP`

**Different fields:** equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | ahu | rat | AHU 2 | Level Roof |
| Actual | ahu | rat | AHU ST-101-3_4 | Level 3 |

---

### Record 45

**Input:** `Drivers.BacnetNetwork.PLANTROOM.AHU_3_4.points.SouthRaTmp`

**Different fields:** equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | ahu | rat | AHU 2 | Level Roof |
| Actual | ahu | rat | AHU PLANTROOM-3_4 | Level Plantroom |

---

### Record 46

**Input:** `DexusBOSData/Meta/101_GEORGE_ST_1020063A/PLANTROOM/HVAC/AHU_3_4/SOUTH_RA_TEMP`

**Different fields:** equipName, level

| Type | gegEquipType | gegPointType | equipName | level |
|------|--------------|--------------|-----------|-------|
| Expected | ahu | rat | AHU 2 | Level Roof |
| Actual | ahu | rat | AHU SOUTH-3_4 | Level 3 |

---

## Summary by Field

- **gegPointType**: 27 mismatches
- **equipName**: 17 mismatches
- **level**: 17 mismatches
- **gegEquipType**: 7 mismatches
