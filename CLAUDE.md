# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a TypeScript-based **building automation data mapping tool** designed to process HVAC/BMS (Building Management System) data. The project uses pnpm as its package manager and focuses on mapping various data point formats to standardized schemas.

## Essential Commands

### Development Workflow
```bash
# Install dependencies
pnpm install

# Run in development mode (uses tsx for TypeScript execution)
pnpm dev

# Build the project (compiles to dist/ directory)
pnpm build

# Run compiled JavaScript
pnpm start

# Type checking without compilation
pnpm type-check
```

### Code Quality
```bash
# Lint code (uses oxlint - fast Rust-based linter)
pnpm lint

# Format code (uses Prettier)
pnpm format

# Check if code is properly formatted
pnpm format:check
```

## Code Architecture

### Data Structure
The project processes HVAC/BMS data with the following key schema (see `data/dataset.csv`):
- **input**: Data source paths/identifiers from various BMS systems
- **gegEquipType**: Equipment classifications (vav, pac, etc.)
- **gegPointType**: Point type mappings (flowsp, zt, startstop, etc.)
- **equipName**: Equipment identifiers
- **level**: Building level organization

### Project Structure
- `src/`: TypeScript source code
- `data/`: Sample datasets and mapping data
- `build/`: Compiled JavaScript output (TypeScript compiles here)
- `docs/`: Documentation (TDD file structure started)
- `scripts/`: Utility scripts directory

### Development Tools Configuration
- **TypeScript**: Configured for Node.js with ES2016 target and CommonJS modules
- **oxlint**: Fast linting with error-level correctness checking and performance warnings
- **Prettier**: Standardized formatting (single quotes, 2-space indentation, 80-char width)
- **No ESLint**: Project uses oxlint instead for faster performance

## Important Notes

### Package Management
- Uses **pnpm** exclusively (v10.13.1) - do not use npm or yarn
- Package manager is locked in package.json

### Testing
- No test framework currently configured
- Empty test script exists in package.json

### Build System
- TypeScript compiles to both `build/` and `dist/` directories
- Source maps are generated for debugging
- Type declarations (.d.ts) are emitted

### Data Processing Context
The project is designed to handle building automation data mapping, specifically:
- Converting between different BMS data formats
- Standardizing equipment type classifications
- Mapping point types across different systems
- Organizing data by building levels and equipment hierarchies