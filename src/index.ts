import 'dotenv/config';
import { MVPMappingTool } from './mvp';

async function main() {
  console.log('Starting MVP Building Automation Data Mapping Tool...\n');
  
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    console.error('Error: OPENAI_API_KEY environment variable is required');
    console.log('Please set your OpenAI API key in .env file');
    process.exit(1);
  }
  
  const tool = new MVPMappingTool(apiKey);
  
  console.log('Testing with sample data from dataset.csv...\n');
  await tool.processTestData();
}

main().catch(console.error);
