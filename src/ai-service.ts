import OpenAI from 'openai';
import { MappingResult, Asset } from './types';

export class AIService {
  private openai: OpenAI;

  constructor(apiKey: string) {
    this.openai = new OpenAI({ apiKey });
  }

  async extractEquipmentName(input: string, assetsIds: string[]): Promise<string> {
    const prompt = `You are a Building Automation System expert. Extract and format the equipment name from this BAS data point.

Input: "${input}"

Valid Equipment Types (from assets database): ${assetsIds.join(', ')}

BAS System Architecture Understanding:
- Building automation paths have hierarchical structure:
  1. Network/Protocol level (e.g., Bacnet, ModbusAsync)
  2. Controller/Gateway level (e.g., L4_3456, GF_CONTROLERS)
  3. Equipment level (e.g., VAV_L5_C6, PAC_L6_04)
  4. Point level (e.g., EFFECT_FLOW_SP, ROOM_TEMP)

- Focus on the EQUIPMENT level, not controller level
- Equipment names contain the actual device location and ID
- Controller paths are for network routing, not equipment identification

Equipment Type Recognition Rules:
- MUST use only equipment types from the valid list above
- Common mappings for building automation:
  - VAV patterns → "vav" (Variable Air Volume)
  - PAC patterns → "pac" (Packaged Air Conditioning)
  - PULSE, Water meter patterns → "wm" (Water Meter)
  - ERV patterns → "pac" (Energy Recovery Ventilator uses PAC type)
  - Pump patterns → "pump"
  - Power meter patterns → "pm"

Analysis approach:
1. Identify the equipment-specific part of the path
2. Determine the correct equipment type from the valid assets list
3. Extract location and unit ID
4. Format as: [VALID_EQUIPMENT_TYPE] [LEVEL]-[UNIT_ID]

Key patterns:
- After colons (:) often comes the actual equipment
- Look for patterns like VAV_L5_C6, PAC_L6_04, ERV/EVR_02_01
- For PULSE meters measuring water (HW/CW patterns) → use "wm" not "pulse"
- For ERV units, use "pac" type with format "PAC EVR-X-Y"

Number formatting rules:
- Remove leading zeros from unit numbers (04 → 4, 03 → 3)
- Keep meaningful zeros (L10 stays L10, not L1)
- Standardize format: [VALID_TYPE] [LEVEL]-[UNIT]

Formatting Rules:
- Equipment type MUST be UPPERCASE (VAV, PAC, WM, not vav, pac, wm)
- Follow format: [UPPERCASE_TYPE] [LEVEL]-[UNIT]
- Remove leading zeros from unit numbers

Examples:
- "Bacnet...3456:VAV_L5_C_6/..." → "VAV L5-C6" (uppercase VAV)
- "...5678:PAC_L6_04/..." → "PAC L6-4" (uppercase PAC, remove leading zero)
- "...PULSE_2/L_11_CW_1" → "WM L11-CW1" (uppercase WM for PULSE measuring water)
- "...ERV/EVR_02_01..." → "PAC EVR-2-1" (uppercase PAC)

Return ONLY the formatted equipment name using valid equipment types in UPPERCASE:`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4.1-mini',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 50,
        temperature: 0.1
      });

      const result = response.choices[0]?.message?.content?.trim() || '';
      // Clean up any quotes or extra formatting
      return result.replace(/['"]/g, '');
    } catch (error) {
      console.warn('AI equipment name extraction failed:', error);
      return '';
    }
  }

  async extractLevel(input: string, equipName: string): Promise<string> {
    const prompt = `You are a Building Automation System expert. Extract the building level where the EQUIPMENT is physically located.

Input: "${input}"
Equipment: "${equipName}"

BAS System Architecture Knowledge:
- In building automation, there are multiple hierarchy levels:
  1. Network/Controller level: Where the controller is located
  2. Equipment level: Where the actual equipment (VAV, PAC, etc.) is installed
  3. Point level: The data point from the equipment

- The equipment name contains the most accurate floor information
- Controller paths may show where the controller sits, not where equipment is
- A controller on one floor can manage equipment on multiple floors

Analysis approach:
1. Identify the equipment name you extracted
2. The floor in the equipment name indicates its physical location
3. Ignore controller or network path floor indicators
4. For special cases (like ERV), deduce from equipment ID pattern

Common patterns:
- Numbered floors: L1, L2, L5, LEVEL_05 → "Level 1", "Level 2", "Level 5"
- Ground floor: LG, GF, GROUND → "Level Ground"
- Basement: B1, BASEMENT → "Level Basement"
- Underground: UG → "Level Underground"

Return format: "Level X" where X is the equipment's physical floor
Output:`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4.1-mini',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 20,
        temperature: 0.0
      });

      const result = response.choices[0]?.message?.content?.trim() || '';
      // Only clean up quotes, let AI handle the formatting
      return result.replace(/['"]/g, '').trim();
    } catch (error) {
      console.warn('AI level extraction failed:', error);
      return '';
    }
  }

  async determineEquipmentType(
    input: string,
    equipName: string,
    assetsIds: string[]
  ): Promise<string> {
    const prompt = `
Given this building automation input: "${input}"
Equipment name extracted: "${equipName}"

Available equipment types from assets database: ${assetsIds.join(', ')}

Instructions:
- Analyze the input pattern and equipment name
- Match it to the most appropriate equipment type ID from the available list
- Use your knowledge of building automation systems to make the best match
- Consider the naming conventions used in the input
- Look for patterns that indicate specific HVAC equipment types
- The equipment type IDs in the assets database represent various building automation equipment

Response format: return only the equipment type ID from the available list, nothing else.
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4.1-mini',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 20,
        temperature: 0.1
      });

      const equipType = response.choices[0]?.message?.content?.trim() || 'unknown';
      
      // Validate that the returned type exists in our assets
      return assetsIds.includes(equipType) ? equipType : 'unknown';
    } catch (error) {
      console.warn('AI equipment type determination failed:', error);
      return 'unknown';
    }
  }

  async determinePointType(
    input: string,
    gegEquipType: string,
    assets: Asset[]
  ): Promise<string> {
    const asset = assets.find(a => a.id === gegEquipType);
    if (!asset) return 'unknown';

    const allSensors = [
      ...asset.mandatorySensors.map(s => s.sensorId),
      ...(asset.optionalSensors?.map(s => s.sensorId) || [])
    ];

    const prompt = `
Given this building automation input: "${input}"
Equipment type: "${gegEquipType}"

Available sensor/point types for this equipment: ${allSensors.join(', ')}

HVAC Control System Understanding:
In building automation, there are two fundamental types of data points:
1. **Measured Values** (Sensors): Read current conditions
2. **Control Values** (Setpoints/Commands): Set desired conditions

Temperature Point Classifications:
- **zt** (Zone Temperature): SENSOR reading current room temperature
  - Purpose: Monitor actual temperature conditions
  - Indicators: TEMP (without SP), Temperature, Tmp (without Sp)
  
- **ztsp** (Zone Temperature Setpoint): CONTROL setting for desired room temperature  
  - Purpose: Control target temperature for comfort
  - Indicators: TEMP_SP, TmpSp, _SP suffix, Setpoint
  
- **ratsp** (Return Air Temperature Setpoint): Controls temperature of air returning to equipment
  - Indicators: RETURN_AIR_TEMP_SP, RAT_SP, RET_AIR_SP
  
- **satsp** (Supply Air Temperature Setpoint): Controls temperature of air supplied by equipment
  - Indicators: SUPPLY_AIR_TEMP_SP, SAT_SP, SUP_AIR_SP

Control System Logic:
- No "_SP" suffix or "Setpoint" → Likely a sensor/measurement value
- Has "_SP" suffix or "Setpoint" → Likely a control/setpoint value
- "ROOM_TEMP" (without SP) → zt (temperature sensor)
- "ROOM_TEMP_SP" (with SP) → ztsp (temperature setpoint control)
- "FAN_CMD", "FanCmd" → startstop (fan control)

Instructions:
- Apply HVAC domain knowledge to distinguish between similar temperature types
- Match the input pattern to the correct sensor type based on system architecture
- Prioritize zone control (ztsp) for room/space temperature patterns
- Use exact pattern matching combined with HVAC understanding

Response format: return only the sensor/point type ID from the available list, nothing else.
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4.1-mini',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 20,
        temperature: 0.1
      });

      const pointType = response.choices[0]?.message?.content?.trim() || 'unknown';
      
      // Validate that the returned type exists in our sensors list
      return allSensors.includes(pointType) ? pointType : 'unknown';
    } catch (error) {
      console.warn('AI point type determination failed:', error);
      return 'unknown';
    }
  }
}