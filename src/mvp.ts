import fs from 'fs';
import path from 'path';
import { DataMapper } from './mapper';
import { AIService } from './ai-service';
import { MappingResult, Asset, MappingResultSchema } from './types';

export class MVPMappingTool {
  private mapper: DataMapper;
  private aiService: AIService;
  private assets: Asset[] = [];
  private assetsIds: string[] = [];

  constructor(openaiApiKey: string) {
    if (!openaiApiKey) {
      throw new Error('OpenAI API key is required for equipment and point type determination');
    }
    this.aiService = new AIService(openaiApiKey);
    this.mapper = new DataMapper(this.aiService);
    this.loadAssets();
  }

  private loadAssets(): void {
    try {
      const assetsPath = path.join(process.cwd(), 'data', 'assets.json');
      const assetsIdsPath = path.join(process.cwd(), 'data', 'assets_id.json');
      
      this.assets = JSON.parse(fs.readFileSync(assetsPath, 'utf-8'));
      const assetsIdsData = JSON.parse(fs.readFileSync(assetsIdsPath, 'utf-8'));
      this.assetsIds = assetsIdsData.ids;
    } catch (error) {
      console.error('Failed to load assets:', error);
    }
  }

  async mapInput(input: string): Promise<MappingResult> {
    // Step 1: Extract equipment name from input using AI
    const equipName = await this.mapper.extractEquipName(input, this.assetsIds);
    
    // Step 2: Extract level from equipment name using AI
    const level = await this.mapper.extractLevel(input, equipName);
    
    // Step 3: Use AI and assets to determine equipment type
    const gegEquipType = await this.aiService.determineEquipmentType(
      input, 
      equipName, 
      this.assetsIds
    );
    
    // Step 4: Use AI with equipment sensors to determine point type
    const gegPointType = await this.aiService.determinePointType(
      input,
      gegEquipType,
      this.assets
    );

    const result: MappingResult = {
      gegEquipType,
      gegPointType,
      equipName,
      level
    };

    const validated = MappingResultSchema.parse(result);
    return validated;
  }

  async processTestData(): Promise<MappingResult[]> {
    try {
      const testPath = path.join(process.cwd(), 'data', 'dataset.csv');
      const testData = fs.readFileSync(testPath, 'utf-8');
      const lines = testData.split('\n').slice(1).filter(line => line.trim());
      
      const results: MappingResult[] = [];
      
      for (const line of lines.slice(0, 5)) {
        const input = line.split(',')[0]?.replace(/"/g, '').trim();
        if (input) {
          const result = await this.mapInput(input);
          results.push(result);
          console.log(`Input: ${input}`);
          console.log(`Result:`, result);
          console.log('---');
        }
      }
      
      return results;
    } catch (error) {
      console.error('Failed to process test data:', error);
      return [];
    }
  }
}