import { z } from 'zod';

export const MappingResultSchema = z.object({
  gegEquipType: z.string(),
  gegPointType: z.string(),
  equipName: z.string(),
  level: z.string(),
});

export type MappingResult = z.infer<typeof MappingResultSchema>;

export interface Asset {
  id: string;
  displayName: string;
  mandatorySensors: Array<{ sensorId: string }>;
  optionalSensors?: Array<{ sensorId: string }>;
}