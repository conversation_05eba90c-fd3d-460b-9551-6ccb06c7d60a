import { AIService } from './ai-service';

export class DataMapper {
  constructor(private aiService: AIService) {}

  async extractEquipName(input: string, assetsIds: string[]): Promise<string> {
    return await this.aiService.extractEquipmentName(input, assetsIds);
  }

  async extractLevel(input: string, equipName: string): Promise<string> {
    return await this.aiService.extractLevel(input, equipName);
  }
}