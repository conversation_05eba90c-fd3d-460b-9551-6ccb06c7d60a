# TDD Specification - MVP Mapping Tool

## Overview
Simple MVP to validate AI-based mapping of Driver format BAS data to standardized schema using LangChain + OpenAI.

## Goal
Transform Driver format inputs from `test.csv` to structured output using pure AI approach.

**Input**: Driver format strings (e.g., `Drivers.BacnetNetwork.L5_VAVS.VAV_L5_C6.points.EffectFlowSp`)

**Output**:
```typescript
interface MappingResult {
  gegEquipType: string;
  gegPointType: string;
  equipName: string;
  level: string;
}
```

## Processing Steps

1. **Extract equipName from input**: Parse the Driver format string to identify equipment name
2. **Extract Level from equipName**: Derive building level information from the equipment identifier
3. **Use assets_id and equipName to get gegEquipType**: Determine equipment type based on asset identification and equipment name
4. **Get all related info of gegEquipType and use whole input to get gegPointType**: 
   - Use gegEquipType (as id) to lookup equipment in assets.json
   - Extract "mandatorySensors" and "optionalSensors" from the matched equipment record
   - Analyze complete input context with mandatorySensors and optionalSensors information to determine point type

## TDD Test Structure

### 1. Schema Validation
```typescript
describe('Output Schema', () => {
  test('validates MappingResult schema with Zod')
  test('accepts any string for gegEquipType')
  test('accepts any string for gegPointType')
})
```

### 2. Step-by-Step Processing
```typescript
describe('Equipment Name Extraction', () => {
  test('extracts equipName from Driver format input')
  test('handles various Driver format patterns')
})

describe('Level Extraction', () => {
  test('extracts level from equipName')
  test('handles different level naming conventions')
})

describe('Equipment Type Mapping', () => {
  test('uses assets_id and equipName to determine gegEquipType')
  test('handles unknown equipment patterns')
})

describe('Point Type Mapping', () => {
  test('looks up equipment by gegEquipType id in assets.json')
  test('extracts mandatorySensors from equipment record')
  test('extracts optionalSensors from equipment record')
  test('uses mandatorySensors and optionalSensors with input to determine gegPointType')
  test('handles equipment not found in assets.json')
  test('handles missing sensor data in equipment record')
})
```

### 3. LangChain/OpenAI Integration
```typescript
describe('AI Mapping Service', () => {
  test('maps Driver input to correct gegEquipType')
  test('maps Driver input to correct gegPointType') 
  test('generates correct equipName format')
  test('extracts and formats level correctly')
})
```

### 3. End-to-End MVP Tests
```typescript
describe('MVP Integration', () => {
  test('processes single Driver input correctly')
  test('processes all test.csv entries correctly')
  test('handles API errors gracefully')
})
```

## AI Model Configuration

**Model**: OpenAI GPT-4.1
- High-accuracy model for complex building automation data mapping
- Supports detailed context analysis for equipment type and point type determination
- Handles multi-step reasoning required for the 4-step processing pipeline

## Dependencies
```json
{
  "dependencies": {
    "langchain": "^0.3.x",
    "openai": "^4.x.x", 
    "zod": "^3.x.x"
  }
}
```