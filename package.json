{"name": "mapping-tool", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "generate-output": "tsx scripts/generate-output.ts", "compare-results": "tsx scripts/compare-results.ts", "lint": "oxlint src", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "type-check": "tsc --noEmit", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "devDependencies": {"@types/node": "^22.7.5", "oxlint": "^1.8.0", "prettier": "^3.6.2", "tsx": "^4.20.3", "typescript": "^5.6.3"}, "dependencies": {"dotenv": "^17.2.1", "langchain": "^0.3.30", "openai": "^5.10.2", "zod": "^4.0.11"}}