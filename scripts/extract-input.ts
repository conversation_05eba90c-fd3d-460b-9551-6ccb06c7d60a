import * as fs from 'fs';
import * as path from 'path';

/**
 * <PERSON><PERSON><PERSON> to extract only the second input from each group of 3 entries (input2) from dataset.csv and create test.csv
 * Pattern: Each equipment point has 3 entries - we want only the middle one (Drivers.* entries)
 */
async function extractInput2Column() {
  const datasetPath = path.join(__dirname, '../data/dataset.csv');
  const outputPath = path.join(__dirname, '../data/test.csv');

  try {
    // Read the dataset file
    const csvContent = fs.readFileSync(datasetPath, 'utf-8');
    const lines = csvContent.trim().split('\n');
    
    if (lines.length === 0) {
      throw new Error('Dataset file is empty');
    }

    // Parse header to find input column index
    const headers = lines[0].split(',');
    const inputColumnIndex = headers.findIndex(header => header.trim() === 'input');
    
    if (inputColumnIndex === -1) {
      throw new Error('Input column not found in dataset');
    }

    console.log(`Found 'input' column at index ${inputColumnIndex}`);
    console.log(`Processing ${lines.length - 1} data rows...`);

    // Extract only input2 values (every 2nd entry in groups of 3, starting from row 2)
    const inputValues = ['input']; // Start with header
    
    // Skip header (index 0), then get every 2nd entry in groups of 3
    // Pattern: row 1,2,3 = group 1, row 4,5,6 = group 2, etc.
    // We want: row 2, row 5, row 8, etc. (formula: 3n + 2, where n starts from 0)
    
    for (let i = 1; i < lines.length; i++) {
      // Check if this is the 2nd row in a group of 3 (input2)
      // Row positions: 1,2,3 -> want 2; 4,5,6 -> want 5; 7,8,9 -> want 8
      // Formula: (i-1) % 3 === 1 means it's the 2nd in the group
      if ((i - 1) % 3 === 1) {
        const columns = lines[i].split(',');
        if (columns.length > inputColumnIndex) {
          const inputValue = columns[inputColumnIndex].trim();
          inputValues.push(inputValue);
          console.log(`✓ Selected row ${i}: ${inputValue.substring(0, 50)}...`);
        }
      }
    }

    // Write to test.csv
    const testCsvContent = inputValues.join('\n');
    fs.writeFileSync(outputPath, testCsvContent, 'utf-8');

    console.log(`\n✅ Successfully created test.csv with ${inputValues.length - 1} input2 entries`);
    console.log(`📁 Output file: ${outputPath}`);
    
    // Show preview of first few entries
    console.log('\n📋 Preview of test.csv (input2 only):');
    const previewLines = inputValues.slice(0, 6); // Header + first 5 entries
    previewLines.forEach((line, index) => {
      console.log(`${index === 0 ? 'Header' : `Entry ${index}`}: ${line}`);
    });
    
    if (inputValues.length > 6) {
      console.log(`... and ${inputValues.length - 6} more entries`);
    }

  } catch (error) {
    console.error('❌ Error extracting input2 column:', error.message);
    process.exit(1);
  }
}

// Run the script
extractInput2Column();