#!/usr/bin/env tsx

import dotenv from 'dotenv';
import { AIService } from '../src/ai-service';
import { readFileSync } from 'fs';

dotenv.config();

async function testCapitalization() {
  const aiService = new AIService(process.env.OPENAI_API_KEY!);
  
  // Load assets_id.json
  const assetsIdsData = JSON.parse(readFileSync('./data/assets_id.json', 'utf-8'));
  const assetsIds: string[] = assetsIdsData.ids;
  
  // Test cases with capitalization issues from current difference.md
  const testCases = [
    {
      input: 'Drivers.BacnetNetwork.L5_VAVS.VAV_L5_C6.points.EffectFlowSp',
      expected: 'VAV L5-C6',
      description: 'Should be uppercase VAV, not lowercase vav'
    },
    {
      input: 'Drivers.BacnetNetwork.L6_VAVS.VAV_L6_C18.points.RmTmp',
      expected: 'VAV L6-C18',
      description: 'Should be uppercase VAV, not lowercase vav'
    },
    {
      input: 'Drivers.BacnetNetwork.L4_VAVS.VAV_L4_C17.points.TerminalLoadOut',
      expected: 'VAV L4-C17',
      description: 'Should be uppercase VAV, not lowercase vav'
    },
    {
      input: 'Drivers.BacnetNetwork.L4_VAVS.VAV_L4_C15.points.DamperPos',
      expected: 'VAV L4-C15',
      description: 'Should be uppercase VAV, not lowercase vav'
    }
  ];

  console.log('Testing Equipment Name Capitalization...\n');
  
  for (const testCase of testCases) {
    try {
      const result = await aiService.extractEquipmentName(testCase.input, assetsIds);
      const isCorrect = result === testCase.expected;
      
      console.log(`${testCase.description}`);
      console.log(`Input: ${testCase.input}`);
      console.log(`Expected: ${testCase.expected}`);
      console.log(`Actual: ${result}`);
      console.log(`✅ ${isCorrect ? 'PASS' : 'FAIL'}\n`);
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error(`Error processing: ${testCase.input}`, error);
    }
  }
}

testCapitalization().catch(console.error);