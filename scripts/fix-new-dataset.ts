#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';

function fixNewDataset() {
  console.log('Fixing new_dataset.csv format...\n');

  // Read the current new_dataset.csv
  const datasetPath = path.join(process.cwd(), 'data', 'new_dataset.csv');
  const data = fs.readFileSync(datasetPath, 'utf-8');
  const lines = data.split('\n').filter(line => line.trim());
  
  console.log(`Current header: ${lines[0]}`);
  console.log(`Total lines: ${lines.length}\n`);
  
  // Process lines
  const newLines: string[] = [];
  
  lines.forEach((line, index) => {
    if (index === 0) {
      // Update header: remove site, change levelName to level
      newLines.push('input,gegEquipType,gegPointType,equipName,level');
    } else {
      // Parse CSV line (handle quoted fields)
      const columns = line.match(/("([^"]|"")*"|[^",]+)/g) || [];
      const cleanColumns = columns.map(col => col.replace(/^"|"$/g, ''));
      
      // Original format: input,site,gegEquipType,gegPointType,equipName,levelName
      // New format: input,gegEquipType,gegPointType,equipName,level
      if (cleanColumns.length >= 6) {
        const newLine = `"${cleanColumns[0]}","${cleanColumns[2]}","${cleanColumns[3]}","${cleanColumns[4]}","${cleanColumns[5]}"`;
        newLines.push(newLine);
      }
    }
  });

  // Write back to file
  fs.writeFileSync(datasetPath, newLines.join('\n') + '\n');

  console.log(`✅ Successfully updated new_dataset.csv`);
  console.log(`📊 Changes made:`);
  console.log(`   - Removed 'site' column`);
  console.log(`   - Changed 'levelName' to 'level'`);
  console.log(`   - New header: input,gegEquipType,gegPointType,equipName,level`);
  console.log(`   - Total records: ${newLines.length - 1} (excluding header)`);
  
  // Show first few examples
  console.log(`\n📝 First 3 records preview:`);
  newLines.slice(1, 4).forEach((line, index) => {
    const columns = line.match(/("([^"]|"")*"|[^",]+)/g) || [];
    const cleanColumns = columns.map(col => col.replace(/^"|"$/g, ''));
    console.log(`   ${index + 1}. Input: ${cleanColumns[0].substring(0, 50)}...`);
    console.log(`      Equipment: ${cleanColumns[3]}, Type: ${cleanColumns[1]}, Level: ${cleanColumns[4]}`);
  });
}

fixNewDataset();