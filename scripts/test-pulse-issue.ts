#!/usr/bin/env tsx

import dotenv from 'dotenv';
import { AIService } from '../src/ai-service';
import { readFileSync } from 'fs';

dotenv.config();

async function testPulseIssue() {
  const aiService = new AIService(process.env.OPENAI_API_KEY!);
  
  // Load assets_id.json
  const assetsIdsData = JSON.parse(readFileSync('./data/assets_id.json', 'utf-8'));
  const assetsIds: string[] = assetsIdsData.ids;
  
  // Test cases with PULSE issues from records 2, 3, 4
  const testCases = [
    {
      input: 'Bacnet-DGEORGE_ST_101_ROOF_1234 1234:PULSE_2/L_11_CW_1',
      expected: 'WM L11-CW1',
      description: 'Record 2 - PULSE water meter should be WM'
    },
    {
      input: 'Drivers.ModbusAsyncNetwork1.Pulse2.points.L11_CW1',
      expected: 'WM L11-CW1',
      description: 'Record 3 - Pulse water meter should be WM'
    },
    {
      input: 'DexusBOSData/Meta/101_GEORGE_ST_1020063A/BUILDING/PULSE_2/L_11_CW_1',
      expected: 'WM L11-CW1',
      description: 'Record 4 - PULSE water meter should be WM'
    }
  ];

  console.log('Testing PULSE Equipment Name Extraction Issues...\n');
  console.log(`Available equipment types: ${assetsIds.slice(0, 10).join(', ')}... (${assetsIds.length} total)\n`);
  
  for (const testCase of testCases) {
    try {
      const result = await aiService.extractEquipmentName(testCase.input, assetsIds);
      const isCorrect = result === testCase.expected;
      
      console.log(`${testCase.description}`);
      console.log(`Input: ${testCase.input}`);
      console.log(`Expected: ${testCase.expected}`);
      console.log(`Actual: ${result}`);
      console.log(`✅ ${isCorrect ? 'PASS' : 'FAIL'}\n`);
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error processing: ${testCase.input}`, error);
    }
  }

  // Show relevant equipment types
  console.log('Water meter related equipment types in assets:');
  console.log(assetsIds.filter(id => id.includes('wm') || id.includes('water')).join(', '));
}

testPulseIssue().catch(console.error);