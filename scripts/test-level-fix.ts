import 'dotenv/config';
import { MVPMappingTool } from '../src/mvp';

async function testLevelFix(): Promise<void> {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    console.error('OPENAI_API_KEY required');
    process.exit(1);
  }

  const tool = new MVPMappingTool(apiKey);

  // Test cases that had level errors
  const testInputs = [
    {
      input: 'Bacnet-DGEORGE_ST_101_L4_3456 3456:VAV_L5_C_6/EFFECT_FLOW_SP',
      expectedLevel: 'Level 5'
    },
    {
      input: 'Bacnet-DGEORGE_ST_101_L4_3456 3456:VAV_L6_C_18/ROOM_TEMP',
      expectedLevel: 'Level 6'
    },
    {
      input: 'Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:PAC_L6_04/ROOM_TEMP_SP',
      expectedLevel: 'Level 6'
    },
    {
      input: 'Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:ERV/EVR_02_01_STATUS',
      expectedLevel: 'Level 2'
    }
  ];

  console.log('Testing improved level extraction with BAS architecture understanding:\n');

  for (const test of testInputs) {
    try {
      const result = await tool.mapInput(test.input);
      const isCorrect = result.level === test.expectedLevel;
      
      console.log(`Input: ${test.input.substring(0, 60)}...`);
      console.log(`Equipment: ${result.equipName}`);
      console.log(`Expected Level: ${test.expectedLevel}`);
      console.log(`Actual Level: ${result.level} ${isCorrect ? '✅' : '❌'}`);
      console.log('---\n');
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error: ${error}`);
    }
  }
}

testLevelFix();