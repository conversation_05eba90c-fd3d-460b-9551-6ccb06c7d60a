import 'dotenv/config';
import { MVPMappingTool } from '../src/mvp';

async function testEquipmentNames(): Promise<void> {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    console.error('OPENAI_API_KEY required');
    process.exit(1);
  }

  const tool = new MVPMappingTool(apiKey);

  const testInputs = [
    'Drivers.BacnetNetwork.L5_VAVS.VAV_L5_C6.points.EffectFlowSp',
    'Drivers.BacnetNetwork.GF_CONTROLERS.MSSB_GF_01.points.PAC_GF_03_FanCmd',
    'DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_02/HVAC/ERV/EVR_02_01_STATUS',
    'Drivers.ModbusAsyncNetwork1.Pulse2.points.L11_CW1'
  ];

  console.log('Testing improved equipment name extraction:\n');

  for (const input of testInputs) {
    try {
      const result = await tool.mapInput(input);
      console.log(`Input: ${input}`);
      console.log(`Equipment Name: ${result.equipName}`);
      console.log(`Equipment Type: ${result.gegEquipType}`);
      console.log(`Level: ${result.level}`);
      console.log('---\n');
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error: ${error}`);
    }
  }
}

testEquipmentNames();