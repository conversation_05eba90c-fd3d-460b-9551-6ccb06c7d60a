#!/usr/bin/env tsx

import dotenv from 'dotenv';
import { AIService } from '../src/ai-service';
import { readFileSync } from 'fs';
import { Asset } from '../src/types';

dotenv.config();

async function testFanCmdIssue() {
  const aiService = new AIService(process.env.OPENAI_API_KEY!);
  
  // Load assets data
  const assetsData = JSON.parse(readFileSync('./data/assets.json', 'utf-8'));
  const assets: Asset[] = assetsData;
  
  // Test the FAN_CMD issue from record 1
  const testCase = {
    input: 'Drivers.BacnetNetwork.GF_CONTROLERS.MSSB_GF_01.points.PAC_GF_03_FanCmd',
    expected: 'startstop',
    description: 'Record 1 - FanCmd should be startstop not speed'
  };

  console.log('Testing FAN_CMD Classification Issue...\n');
  
  try {
    const result = await aiService.determinePointType(testCase.input, 'pac', assets);
    const isCorrect = result === testCase.expected;
    
    console.log(`${testCase.description}`);
    console.log(`Input: ${testCase.input}`);
    console.log(`Expected: ${testCase.expected} (Start/Stop Control)`);
    console.log(`Actual: ${result}`);
    console.log(`✅ ${isCorrect ? 'PASS' : 'FAIL'}\n`);
  } catch (error) {
    console.error(`Error processing: ${testCase.input}`, error);
  }

  // Show available fan-related sensors for PAC
  const pacAsset = assets.find(a => a.id === 'pac');
  if (pacAsset) {
    const allSensors = [
      ...pacAsset.mandatorySensors.map(s => s.sensorId),
      ...(pacAsset.optionalSensors?.map(s => s.sensorId) || [])
    ];
    
    console.log('Available fan/speed related sensors for PAC equipment:');
    console.log(allSensors.filter(s => s.includes('fan') || s.includes('speed') || s.includes('start')).sort().join(', '));
  }
}

testFanCmdIssue().catch(console.error);