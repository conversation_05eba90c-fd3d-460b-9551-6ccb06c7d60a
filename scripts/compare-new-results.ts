#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';

interface DatasetRecord {
  input: string;
  gegEquipType: string;
  gegPointType: string;
  equipName: string;
  level: string;
}

interface ComparisonResult {
  input: string;
  expectedRecord: DatasetRecord;
  actualRecord: DatasetRecord;
  differences: string[];
}

function parseCSVLine(line: string): string[] {
  const columns = line.match(/("([^"]|"")*"|[^",]+)/g) || [];
  return columns.map(col => col.replace(/^"|"$/g, ''));
}

function compareNewResults() {
  console.log('Comparing new_dataset.csv with new_output.csv...\n');

  // Read expected results (new_dataset.csv)
  const expectedPath = path.join(process.cwd(), 'data', 'new_dataset.csv');
  const expectedData = fs.readFileSync(expectedPath, 'utf-8');
  const expectedLines = expectedData.split('\n').filter(line => line.trim());

  // Read actual results (new_output.csv)
  const actualPath = path.join(process.cwd(), 'data', 'new_output.csv');
  const actualData = fs.readFileSync(actualPath, 'utf-8');
  const actualLines = actualData.split('\n').filter(line => line.trim());

  console.log(`Expected records: ${expectedLines.length - 1}`);
  console.log(`Actual records: ${actualLines.length - 1}`);

  // Parse expected records
  const expectedRecords: DatasetRecord[] = [];
  for (let i = 1; i < expectedLines.length; i++) {
    const columns = parseCSVLine(expectedLines[i]);
    if (columns.length >= 5) {
      expectedRecords.push({
        input: columns[0],
        gegEquipType: columns[1],
        gegPointType: columns[2],
        equipName: columns[3],
        level: columns[4]
      });
    }
  }

  // Parse actual records
  const actualRecords: DatasetRecord[] = [];
  for (let i = 1; i < actualLines.length; i++) {
    const columns = parseCSVLine(actualLines[i]);
    if (columns.length >= 5) {
      actualRecords.push({
        input: columns[0],
        gegEquipType: columns[1],
        gegPointType: columns[2],
        equipName: columns[3],
        level: columns[4]
      });
    }
  }

  // Compare records
  const mismatches: ComparisonResult[] = [];
  const minLength = Math.min(expectedRecords.length, actualRecords.length);

  for (let i = 0; i < minLength; i++) {
    const expected = expectedRecords[i];
    const actual = actualRecords[i];
    const differences: string[] = [];

    // Check each field
    if (expected.gegEquipType !== actual.gegEquipType) {
      differences.push('gegEquipType');
    }
    if (expected.gegPointType !== actual.gegPointType) {
      differences.push('gegPointType');
    }
    if (expected.equipName !== actual.equipName) {
      differences.push('equipName');
    }
    if (expected.level !== actual.level) {
      differences.push('level');
    }

    if (differences.length > 0) {
      mismatches.push({
        input: expected.input,
        expectedRecord: expected,
        actualRecord: actual,
        differences
      });
    }
  }

  console.log(`Found ${mismatches.length} mismatched records with ${mismatches.reduce((sum, m) => sum + m.differences.length, 0)} total differences\n`);

  // Generate difference report
  const reportLines: string[] = [
    '# Comparison Results: New Dataset vs New Output',
    '',
    '**Summary:**',
    `- Expected records: ${expectedRecords.length}`,
    `- Actual records: ${actualRecords.length}`,
    `- Mismatched records: ${mismatches.length}`,
    `- Total field differences: ${mismatches.reduce((sum, m) => sum + m.differences.length, 0)}`,
    ''
  ];

  if (mismatches.length > 0) {
    reportLines.push('## Mismatched Records', '');

    mismatches.forEach((mismatch, index) => {
      reportLines.push(`### Record ${index + 1}`, '');
      reportLines.push(`**Input:** \`${mismatch.input}\``, '');
      reportLines.push(`**Different fields:** ${mismatch.differences.join(', ')}`, '');
      reportLines.push('| Type | gegEquipType | gegPointType | equipName | level |');
      reportLines.push('|------|--------------|--------------|-----------|-------|');
      reportLines.push(`| Expected | ${mismatch.expectedRecord.gegEquipType} | ${mismatch.expectedRecord.gegPointType} | ${mismatch.expectedRecord.equipName} | ${mismatch.expectedRecord.level} |`);
      reportLines.push(`| Actual | ${mismatch.actualRecord.gegEquipType} | ${mismatch.actualRecord.gegPointType} | ${mismatch.actualRecord.equipName} | ${mismatch.actualRecord.level} |`);
      reportLines.push('', '---', '');
    });

    // Summary by field
    const fieldCounts: { [key: string]: number } = {};
    mismatches.forEach(m => {
      m.differences.forEach(diff => {
        fieldCounts[diff] = (fieldCounts[diff] || 0) + 1;
      });
    });

    reportLines.push('## Summary by Field', '');
    Object.entries(fieldCounts)
      .sort(([,a], [,b]) => b - a)
      .forEach(([field, count]) => {
        reportLines.push(`- **${field}**: ${count} mismatches`);
      });
    reportLines.push('');
  } else {
    reportLines.push('🎉 **Perfect Match!** All records matched exactly.');
  }

  // Write report
  const reportPath = path.join(process.cwd(), 'new_difference.md');
  fs.writeFileSync(reportPath, reportLines.join('\n'));

  console.log(`✅ Comparison complete! Report saved to ${reportPath}`);
}

compareNewResults();