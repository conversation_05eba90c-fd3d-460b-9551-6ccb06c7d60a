#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';

interface GeorgeDatasetRow {
  input1: string;
  input2: string;
  input3: string;
  site: string;
  gegEquipType: string;
  gegPointType: string;
  equipName: string;
  levelName: string;
}

interface NewDatasetRow {
  input: string;
  site: string;
  gegEquipType: string;
  gegPointType: string;
  equipName: string;
  levelName: string;
}

function createNewDataset() {
  console.log('Creating new dataset from george_dataset.csv...\n');

  // Read the original dataset
  const georgePath = path.join(process.cwd(), 'data', 'george_dataset.csv');
  const georgeData = fs.readFileSync(georgePath, 'utf-8');
  const lines = georgeData.split('\n').filter(line => line.trim());
  
  // Parse header
  const header = lines[0];
  console.log(`Original header: ${header}`);
  
  // Take first 50 data rows (skip header)
  const dataRows = lines.slice(1, 51);
  console.log(`Taking first ${dataRows.length} rows from george_dataset.csv\n`);
  
  // Parse each row
  const georgeRows: GeorgeDatasetRow[] = dataRows.map(line => {
    const columns = line.split(',');
    return {
      input1: columns[0],
      input2: columns[1], 
      input3: columns[2],
      site: columns[3],
      gegEquipType: columns[4],
      gegPointType: columns[5],
      equipName: columns[6],
      levelName: columns[7]
    };
  });

  // Create new dataset rows - 3 records per original row
  const newRows: NewDatasetRow[] = [];
  
  georgeRows.forEach((row, index) => {
    // Record 1: using input1
    newRows.push({
      input: row.input1,
      site: row.site,
      gegEquipType: row.gegEquipType,
      gegPointType: row.gegPointType,
      equipName: row.equipName,
      levelName: row.levelName
    });
    
    // Record 2: using input2  
    newRows.push({
      input: row.input2,
      site: row.site,
      gegEquipType: row.gegEquipType,
      gegPointType: row.gegPointType,
      equipName: row.equipName,
      levelName: row.levelName
    });
    
    // Record 3: using input3
    newRows.push({
      input: row.input3,
      site: row.site,
      gegEquipType: row.gegEquipType,
      gegPointType: row.gegPointType,
      equipName: row.equipName,
      levelName: row.levelName
    });
    
    if ((index + 1) % 10 === 0) {
      console.log(`Processed ${index + 1}/${georgeRows.length} original rows...`);
    }
  });

  // Create CSV content
  const newHeader = 'input,site,gegEquipType,gegPointType,equipName,levelName';
  const csvLines = [newHeader];
  
  newRows.forEach(row => {
    const line = `"${row.input}","${row.site}","${row.gegEquipType}","${row.gegPointType}","${row.equipName}","${row.levelName}"`;
    csvLines.push(line);
  });

  // Write to new file
  const newDatasetPath = path.join(process.cwd(), 'data', 'new_dataset.csv');
  fs.writeFileSync(newDatasetPath, csvLines.join('\n') + '\n');

  console.log(`\n✅ Successfully created new_dataset.csv with ${newRows.length} records`);
  console.log(`📊 Dataset structure:`);
  console.log(`   - Original records: ${georgeRows.length}`);
  console.log(`   - New records: ${newRows.length} (${georgeRows.length} × 3)`);
  console.log(`   - File saved to: ${newDatasetPath}`);
  
  // Show first few examples
  console.log(`\n📝 First 3 records preview:`);
  newRows.slice(0, 3).forEach((row, index) => {
    console.log(`   ${index + 1}. Input: ${row.input.substring(0, 60)}...`);
    console.log(`      Equipment: ${row.equipName}, Type: ${row.gegEquipType}, Point: ${row.gegPointType}`);
  });
}

createNewDataset();