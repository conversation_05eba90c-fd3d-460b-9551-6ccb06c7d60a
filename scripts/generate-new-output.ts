#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { MVPMappingTool } from '../src/mvp';

dotenv.config();

async function generateNewOutput() {
  const openaiApiKey = process.env.OPENAI_API_KEY;
  if (!openaiApiKey) {
    console.error('Error: OPENAI_API_KEY environment variable is not set');
    process.exit(1);
  }

  console.log('Starting new dataset processing...\n');

  // Read the new dataset
  const datasetPath = path.join(process.cwd(), 'data', 'new_dataset.csv');
  const data = fs.readFileSync(datasetPath, 'utf-8');
  const lines = data.split('\n').filter(line => line.trim());
  
  console.log(`Found ${lines.length - 1} inputs to process\n`);

  // Initialize the MVP mapping tool
  const mappingTool = new MVPMappingTool(openaiApiKey);

  // Prepare CSV output
  const outputPath = path.join(process.cwd(), 'data', 'new_output.csv');
  const csvHeader = 'input,gegEquipType,gegPointType,equipName,level';
  const csvRows = [csvHeader];

  // Process each input (skip header)
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    
    // Parse CSV line (handle quoted fields)
    const columns = line.match(/("([^"]|"")*"|[^",]+)/g) || [];
    const input = columns[0]?.replace(/^"|"$/g, '').trim();
    
    if (!input) continue;

    try {
      console.log(`Processing ${i}/${lines.length - 1}: ${input.substring(0, 60)}...`);
      
      const result = await mappingTool.mapInput(input);
      const csvRow = `"${input}","${result.gegEquipType}","${result.gegPointType}","${result.equipName}","${result.level}"`;
      csvRows.push(csvRow);
      
      console.log(`  Result: ${result.gegEquipType} | ${result.gegPointType} | ${result.equipName} | ${result.level}`);
      
      // Add delay to avoid rate limiting (500ms between requests)
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error(`Error processing input ${i}: ${input}`);
      console.error('Error details:', error);
      
      // Add placeholder row for failed processing
      const csvRow = `"${input}","error","error","error","error"`;
      csvRows.push(csvRow);
    }
  }

  // Write results to file
  fs.writeFileSync(outputPath, csvRows.join('\n') + '\n');

  console.log(`\n✅ Processing complete! Results saved to ${outputPath}`);
  console.log(`Processed ${lines.length - 1} inputs`);
}

generateNewOutput().catch(console.error);