#!/usr/bin/env tsx

import dotenv from 'dotenv';
import { AIService } from '../src/ai-service';
import { readFileSync } from 'fs';
import { Asset } from '../src/types';

dotenv.config();

async function testControlSystemLogic() {
  const aiService = new AIService(process.env.OPENAI_API_KEY!);
  
  // Load assets data
  const assetsData = JSON.parse(readFileSync('./data/assets.json', 'utf-8'));
  const assets: Asset[] = assetsData;
  
  // Test cases to validate HVAC control system understanding
  const testCases = [
    {
      input: 'DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_06/HVAC/VAV_L6_C_18/ROOM_TEMP',
      expected: 'zt',
      description: 'ROOM_TEMP (no _SP) should be zt (sensor reading)'
    },
    {
      input: 'Bacnet-DGEORGE_ST_101_L4_3456 3456:VAV_L5_C_2/ROOM_TEMP_SP',
      expected: 'ztsp',
      description: 'ROOM_TEMP_SP (with _SP) should be ztsp (setpoint control)'
    },
    {
      input: 'Drivers.BacnetNetwork.L5_VAVS.VAV_L5_C2.points.RmTmpSp',
      expected: 'ztsp',
      description: 'RmTmpSp (with Sp) should be ztsp (setpoint control)'
    },
    {
      input: 'Drivers.BacnetNetwork.L6_VAVS.VAV_L6_C18.points.RmTmp',
      expected: 'zt',
      description: 'RmTmp (no Sp) should be zt (sensor reading)'
    }
  ];

  console.log('Testing HVAC Control System Logic Understanding...\n');
  
  for (const testCase of testCases) {
    try {
      const result = await aiService.determinePointType(testCase.input, 'vav', assets);
      const isCorrect = result === testCase.expected;
      
      console.log(`${testCase.description}`);
      console.log(`Input: ${testCase.input}`);
      console.log(`Expected: ${testCase.expected} (${testCase.expected === 'zt' ? 'Temperature Sensor' : 'Temperature Setpoint'})`);
      console.log(`Actual: ${result}`);
      console.log(`✅ ${isCorrect ? 'PASS' : 'FAIL'}\n`);
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error processing: ${testCase.input}`, error);
    }
  }

  // Show available temperature sensors for VAV
  const vavAsset = assets.find(a => a.id === 'vav');
  if (vavAsset) {
    const allSensors = [
      ...vavAsset.mandatorySensors.map(s => s.sensorId),
      ...(vavAsset.optionalSensors?.map(s => s.sensorId) || [])
    ];
    
    console.log('Available temperature sensors for VAV equipment:');
    console.log(allSensors.filter(s => s.includes('zt')).sort().join(', '));
  }
}

testControlSystemLogic().catch(console.error);