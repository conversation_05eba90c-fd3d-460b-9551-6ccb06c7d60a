import fs from 'fs';
import path from 'path';

interface Asset {
  id: string;
  [key: string]: any;
}

async function extractAssetsIds(): Promise<void> {
  try {
    // Read assets.json file
    const assetsPath = path.join(process.cwd(), 'data', 'assets.json');
    const assetsData = fs.readFileSync(assetsPath, 'utf-8');
    const assets: Asset[] = JSON.parse(assetsData);

    // Extract all id fields
    const assetIds = assets.map(asset => asset.id);

    // Create output object
    const output = {
      totalCount: assetIds.length,
      ids: assetIds
    };

    // Save to data/assets_id.json
    const outputPath = path.join(process.cwd(), 'data', 'assets_id.json');
    fs.writeFileSync(outputPath, JSON.stringify(output, null, 2), 'utf-8');

    console.log(`Successfully extracted ${assetIds.length} asset IDs to ${outputPath}`);
    console.log('Asset IDs:', assetIds);
  } catch (error) {
    console.error('Error extracting asset IDs:', error);
    process.exit(1);
  }
}

extractAssetsIds();