import fs from 'fs';
import path from 'path';

interface DataRow {
  input: string;
  gegEquipType: string;
  gegPointType: string;
  equipName: string;
  level: string;
}

function parseCSV(csvContent: string): DataRow[] {
  const lines = csvContent.split('\n').filter(line => line.trim());
  const rows: DataRow[] = [];
  
  // Skip header
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    if (!line.trim()) continue;
    
    // Parse CSV with quoted fields
    const columns: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let j = 0; j < line.length; j++) {
      const char = line[j];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        columns.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    columns.push(current.trim()); // Add last column
    
    if (columns.length >= 5) {
      rows.push({
        input: columns[0],
        gegEquipType: columns[1],
        gegPointType: columns[2],
        equipName: columns[3],
        level: columns[4]
      });
    }
  }
  
  return rows;
}

async function compareResults(): Promise<void> {
  try {
    // Read both files
    const datasetPath = path.join(process.cwd(), 'data', 'dataset.csv');
    const outputPath = path.join(process.cwd(), 'data', 'output.csv');
    
    const expectedData = fs.readFileSync(datasetPath, 'utf-8');
    const actualData = fs.readFileSync(outputPath, 'utf-8');
    
    const expected = parseCSV(expectedData);
    const actual = parseCSV(actualData);
    
    console.log(`Expected records: ${expected.length}`);
    console.log(`Actual records: ${actual.length}`);
    
    // Compare records and find mismatched records
    const mismatchedRecords: Array<{
      input: string;
      expected: DataRow;
      actual: DataRow;
      differences: string[];
    }> = [];
    
    const minLength = Math.min(expected.length, actual.length);
    
    for (let i = 0; i < minLength; i++) {
      const exp = expected[i];
      const act = actual[i];
      
      const recordDifferences: string[] = [];
      
      // Check each field
      if (exp.gegEquipType !== act.gegEquipType) {
        recordDifferences.push('gegEquipType');
      }
      if (exp.gegPointType !== act.gegPointType) {
        recordDifferences.push('gegPointType');
      }
      if (exp.equipName !== act.equipName) {
        recordDifferences.push('equipName');
      }
      if (exp.level !== act.level) {
        recordDifferences.push('level');
      }
      
      // If any differences found, add the whole record
      if (recordDifferences.length > 0) {
        mismatchedRecords.push({
          input: exp.input,
          expected: exp,
          actual: act,
          differences: recordDifferences
        });
      }
    }
    
    const totalDifferences = mismatchedRecords.reduce((sum, record) => sum + record.differences.length, 0);
    console.log(`Found ${mismatchedRecords.length} mismatched records with ${totalDifferences} total differences`);
    
    // Generate Markdown report
    let markdown = `# Comparison Results: Dataset vs Output\n\n`;
    markdown += `**Summary:**\n`;
    markdown += `- Expected records: ${expected.length}\n`;
    markdown += `- Actual records: ${actual.length}\n`;
    markdown += `- Mismatched records: ${mismatchedRecords.length}\n`;
    markdown += `- Total field differences: ${totalDifferences}\n\n`;
    
    if (mismatchedRecords.length === 0) {
      markdown += `✅ **No differences found!** All records match perfectly.\n`;
    } else {
      markdown += `## Mismatched Records\n\n`;
      
      for (let i = 0; i < mismatchedRecords.length; i++) {
        const record = mismatchedRecords[i];
        markdown += `### Record ${i + 1}\n\n`;
        markdown += `**Input:** \`${record.input}\`\n\n`;
        markdown += `**Different fields:** ${record.differences.join(', ')}\n\n`;
        
        markdown += `| Type | gegEquipType | gegPointType | equipName | level |\n`;
        markdown += `|------|--------------|--------------|-----------|-------|\n`;
        markdown += `| Expected | ${record.expected.gegEquipType} | ${record.expected.gegPointType} | ${record.expected.equipName} | ${record.expected.level} |\n`;
        markdown += `| Actual | ${record.actual.gegEquipType} | ${record.actual.gegPointType} | ${record.actual.equipName} | ${record.actual.level} |\n\n`;
        
        markdown += `---\n\n`;
      }
      
      // Summary by field type
      const fieldCounts = mismatchedRecords.reduce((acc, record) => {
        record.differences.forEach(field => {
          acc[field] = (acc[field] || 0) + 1;
        });
        return acc;
      }, {} as Record<string, number>);
      
      markdown += `## Summary by Field\n\n`;
      for (const [field, count] of Object.entries(fieldCounts)) {
        markdown += `- **${field}**: ${count} mismatches\n`;
      }
    }
    
    // Save to difference.md
    const differencePath = path.join(process.cwd(), 'difference.md');
    fs.writeFileSync(differencePath, markdown, 'utf-8');
    
    console.log(`\n✅ Comparison complete! Report saved to ${differencePath}`);
    
  } catch (error) {
    console.error('Error comparing results:', error);
    process.exit(1);
  }
}

compareResults();