#!/usr/bin/env tsx

import dotenv from 'dotenv';
import { AIService } from '../src/ai-service';

dotenv.config();

async function testLeadingZeros() {
  const aiService = new AIService(process.env.OPENAI_API_KEY!);
  
  // Test cases with leading zero issues from difference.md
  const testCases = [
    {
      input: 'Bacnet-DGEORGE_ST_101_GF_4567 4567:MSSB_GF_01/PAC_GF_03_FAN_CMD',
      expected: 'PAC GF-3'
    },
    {
      input: 'Drivers.BacnetNetwork.GF_CONTROLERS.MSSB_GF_01.points.PAC_GF_03_FanCmd',
      expected: 'PAC GF-3'
    },
    {
      input: 'DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_GF/HVAC/MSSB_GF_01/PAC_GF_03_FAN_CMD',
      expected: 'PAC GF-3'
    },
    {
      input: 'Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:PAC_L6_04/ROOM_TEMP_SP',
      expected: 'PAC L6-4'
    },
    {
      input: 'Drivers.BacnetNetwork.LEVEL_6.PAC_L6_04.points.RmTmpSp',
      expected: 'PAC L6-4'
    },
    {
      input: 'DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_06/HVAC/PAC_L6_04/ROOM_TEMP_SP',
      expected: 'PAC L6-4'
    }
  ];

  console.log('Testing Leading Zero Formatting...\n');
  
  for (const testCase of testCases) {
    try {
      const result = await aiService.extractEquipmentName(testCase.input);
      const isCorrect = result === testCase.expected;
      
      console.log(`Input: ${testCase.input}`);
      console.log(`Expected: ${testCase.expected}`);
      console.log(`Actual: ${result}`);
      console.log(`✅ ${isCorrect ? 'PASS' : 'FAIL'}\n`);
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error(`Error processing: ${testCase.input}`, error);
    }
  }
}

testLeadingZeros().catch(console.error);