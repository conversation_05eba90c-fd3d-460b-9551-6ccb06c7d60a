#!/usr/bin/env tsx

import dotenv from 'dotenv';
import { AIService } from '../src/ai-service';
import { readFileSync } from 'fs';
import { Asset } from '../src/types';

dotenv.config();

async function testPointTypeIssue() {
  const aiService = new AIService(process.env.OPENAI_API_KEY!);
  
  // Load assets data
  const assetsData = JSON.parse(readFileSync('./data/assets.json', 'utf-8'));
  const assets: Asset[] = assetsData;
  
  // Test the problematic inputs from records 5, 6, 7
  const testCases = [
    {
      input: 'Bacnet-DGEORGE_ST_101_L4_PAC_5678 5678:PAC_L6_04/ROOM_TEMP_SP',
      expected: 'ztsp',
      description: 'Record 5 - ROOM_TEMP_SP should be zone temp setpoint'
    },
    {
      input: 'Drivers.BacnetNetwork.LEVEL_6.PAC_L6_04.points.RmTmpSp',
      expected: 'ztsp', 
      description: 'Record 6 - RmTmpSp should be zone temp setpoint'
    },
    {
      input: 'DexusBOSData/Meta/101_GEORGE_ST_1020063A/LEVEL_06/HVAC/PAC_L6_04/ROOM_TEMP_SP',
      expected: 'ztsp',
      description: 'Record 7 - ROOM_TEMP_SP should be zone temp setpoint'
    }
  ];

  console.log('Testing Point Type Classification Issues...\n');
  
  for (const testCase of testCases) {
    try {
      const result = await aiService.determinePointType(testCase.input, 'pac', assets);
      const isCorrect = result === testCase.expected;
      
      console.log(`${testCase.description}`);
      console.log(`Input: ${testCase.input}`);
      console.log(`Expected: ${testCase.expected} (Zone Temperature Setpoint)`);
      console.log(`Actual: ${result} (${result === 'ratsp' ? 'Return Air Temperature Setpoint' : result})`);
      console.log(`✅ ${isCorrect ? 'PASS' : 'FAIL'}\n`);
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error processing: ${testCase.input}`, error);
    }
  }

  // Also show what sensors are available for PAC
  const pacAsset = assets.find(a => a.id === 'pac');
  if (pacAsset) {
    const allSensors = [
      ...pacAsset.mandatorySensors.map(s => s.sensorId),
      ...(pacAsset.optionalSensors?.map(s => s.sensorId) || [])
    ];
    
    console.log('Available sensors for PAC equipment:');
    console.log(allSensors.filter(s => s.includes('tsp')).sort().join(', '));
    console.log('\nAll temperature related sensors:');
    console.log(allSensors.filter(s => s.includes('temp') || s.includes('tsp')).sort().join(', '));
  }
}

testPointTypeIssue().catch(console.error);