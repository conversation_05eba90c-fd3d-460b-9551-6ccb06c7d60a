#!/usr/bin/env tsx

import fs from 'fs';
import path from 'path';

interface GeorgeDatasetRow {
  input1: string;
  input2: string;
  input3: string;
  site: string;
  gegEquipType: string;
  gegPointType: string;
  equipName: string;
  levelName: string;
}

interface NewDatasetRow {
  input: string;
  gegEquipType: string;
  gegPointType: string;
  equipName: string;
  level: string;
}

function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

function createRandomDataset() {
  console.log('Creating new dataset with 50 randomly selected points from george_dataset.csv...\n');

  // Read the original dataset
  const georgePath = path.join(process.cwd(), 'data', 'george_dataset.csv');
  const georgeData = fs.readFileSync(georgePath, 'utf-8');
  const lines = georgeData.split('\n').filter(line => line.trim());
  
  // Parse header
  const header = lines[0];
  console.log(`Original header: ${header}`);
  
  // Get all data rows (skip header)
  const allDataRows = lines.slice(1);
  console.log(`Total available records: ${allDataRows.length}`);
  
  // Randomly shuffle and take first 50
  const shuffledRows = shuffleArray(allDataRows);
  const selectedRows = shuffledRows.slice(0, 50);
  console.log(`Randomly selected ${selectedRows.length} records\n`);
  
  // Parse each selected row
  const georgeRows: GeorgeDatasetRow[] = selectedRows.map(line => {
    const columns = line.split(',');
    return {
      input1: columns[0],
      input2: columns[1], 
      input3: columns[2],
      site: columns[3],
      gegEquipType: columns[4],
      gegPointType: columns[5],
      equipName: columns[6],
      levelName: columns[7]
    };
  });

  // Create new dataset rows - 3 records per original row
  const newRows: NewDatasetRow[] = [];
  
  georgeRows.forEach((row, index) => {
    // Record 1: using input1
    newRows.push({
      input: row.input1,
      gegEquipType: row.gegEquipType,
      gegPointType: row.gegPointType,
      equipName: row.equipName,
      level: row.levelName
    });
    
    // Record 2: using input2  
    newRows.push({
      input: row.input2,
      gegEquipType: row.gegEquipType,
      gegPointType: row.gegPointType,
      equipName: row.equipName,
      level: row.levelName
    });
    
    // Record 3: using input3
    newRows.push({
      input: row.input3,
      gegEquipType: row.gegEquipType,
      gegPointType: row.gegPointType,
      equipName: row.equipName,
      level: row.levelName
    });
    
    if ((index + 1) % 10 === 0) {
      console.log(`Processed ${index + 1}/${georgeRows.length} selected rows...`);
    }
  });

  // Create CSV content
  const newHeader = 'input,gegEquipType,gegPointType,equipName,level';
  const csvLines = [newHeader];
  
  newRows.forEach(row => {
    const line = `"${row.input}","${row.gegEquipType}","${row.gegPointType}","${row.equipName}","${row.level}"`;
    csvLines.push(line);
  });

  // Write to new file
  const newDatasetPath = path.join(process.cwd(), 'data', 'new_dataset.csv');
  fs.writeFileSync(newDatasetPath, csvLines.join('\n') + '\n');

  console.log(`\n✅ Successfully created new_dataset.csv with ${newRows.length} records`);
  console.log(`📊 Dataset structure:`);
  console.log(`   - Randomly selected records: ${georgeRows.length}`);
  console.log(`   - New records: ${newRows.length} (${georgeRows.length} × 3)`);
  console.log(`   - File saved to: ${newDatasetPath}`);
  
  // Show first few examples
  console.log(`\n📝 First 3 records preview:`);
  newRows.slice(0, 3).forEach((row, index) => {
    console.log(`   ${index + 1}. Input: ${row.input.substring(0, 60)}...`);
    console.log(`      Equipment: ${row.equipName}, Type: ${row.gegEquipType}, Point: ${row.gegPointType}`);
  });

  // Show some statistics about the random selection
  const equipTypes = [...new Set(georgeRows.map(r => r.gegEquipType))];
  const pointTypes = [...new Set(georgeRows.map(r => r.gegPointType))];
  console.log(`\n📈 Random selection statistics:`);
  console.log(`   - Equipment types: ${equipTypes.join(', ')}`);
  console.log(`   - Point types: ${pointTypes.length} unique types`);
  console.log(`   - Levels: ${[...new Set(georgeRows.map(r => r.levelName))].join(', ')}`);
}

createRandomDataset();