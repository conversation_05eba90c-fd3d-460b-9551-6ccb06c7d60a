import 'dotenv/config';
import fs from 'fs';
import path from 'path';
import { MVPMappingTool } from '../src/mvp';

async function generateOutput(): Promise<void> {
  console.log('Starting dataset processing...\n');

  // Check API key
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    console.error('Error: OPENAI_API_KEY environment variable is required');
    process.exit(1);
  }

  // Initialize the mapping tool
  const tool = new MVPMappingTool(apiKey);

  try {
    // Read dataset.csv
    const datasetPath = path.join(process.cwd(), 'data', 'dataset.csv');
    const csvData = fs.readFileSync(datasetPath, 'utf-8');
    const lines = csvData.split('\n').filter(line => line.trim());
    
    // Skip header and get input column
    const inputs = lines.slice(1).map(line => {
      const columns = line.split(',');
      return columns[0]?.replace(/"/g, '').trim();
    }).filter(input => input);

    console.log(`Found ${inputs.length} inputs to process\n`);

    // Process each input
    const results: string[] = [];
    results.push('input,gegEquipType,gegPointType,equipName,level'); // CSV header

    for (let i = 0; i < inputs.length; i++) {
      const input = inputs[i];
      console.log(`Processing ${i + 1}/${inputs.length}: ${input.substring(0, 50)}...`);

      try {
        const result = await tool.mapInput(input);
        
        // Escape commas in fields and create CSV row
        const csvRow = [
          `"${input}"`,
          `"${result.gegEquipType}"`,
          `"${result.gegPointType}"`,
          `"${result.equipName}"`,
          `"${result.level}"`
        ].join(',');
        
        results.push(csvRow);
        
        console.log(`  Result: ${result.gegEquipType} | ${result.gegPointType} | ${result.equipName} | ${result.level}`);
        
        // Add small delay to avoid hitting rate limits
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        console.error(`  Error processing input: ${error}`);
        // Add error row
        const errorRow = [
          `"${input}"`,
          '"error"',
          '"error"', 
          '"error"',
          '"error"'
        ].join(',');
        results.push(errorRow);
      }
    }

    // Save output.csv
    const outputPath = path.join(process.cwd(), 'data', 'output.csv');
    fs.writeFileSync(outputPath, results.join('\n'), 'utf-8');
    
    console.log(`\n✅ Processing complete! Results saved to ${outputPath}`);
    console.log(`Processed ${inputs.length} inputs`);
    
  } catch (error) {
    console.error('Error processing dataset:', error);
    process.exit(1);
  }
}

generateOutput();