[{"armsAssetTypeId": 574, "displayName": "AHU", "id": "ahu", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "aocct"}, {"extraSkysparkMarkerTags": [], "sensorId": "aohct"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwt"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "dewpoint"}, {"extraSkysparkMarkerTags": [], "sensorId": "dewpointsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ead"}, {"extraSkysparkMarkerTags": [], "sensorId": "economymode"}, {"extraSkysparkMarkerTags": [], "sensorId": "edhstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "<PERSON><PERSON><PERSON>us"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterstatus"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "hchwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwt"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "marh"}, {"extraSkysparkMarkerTags": [], "sensorId": "masp"}, {"extraSkysparkMarkerTags": [], "sensorId": "mat"}, {"extraSkysparkMarkerTags": [], "sensorId": "moad"}, {"extraSkysparkMarkerTags": [], "sensorId": "moadfeedback"}, {"extraSkysparkMarkerTags": [], "sensorId": "mode"}, {"extraSkysparkMarkerTags": [], "sensorId": "oad"}, {"extraSkysparkMarkerTags": [], "sensorId": "oaflow"}, {"extraSkysparkMarkerTags": [], "sensorId": "oarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "oat"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": [], "sensorId": "raco2"}, {"extraSkysparkMarkerTags": [], "sensorId": "rad"}, {"extraSkysparkMarkerTags": [], "sensorId": "rarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "ratsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarhsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": [], "sensorId": "satsp"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwfsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "oae"}, {"extraSkysparkMarkerTags": [], "sensorId": "raco2sp"}, {"extraSkysparkMarkerTags": [], "sensorId": "rae"}, {"extraSkysparkMarkerTags": [], "sensorId": "rms"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergythermal"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalpowerthermal"}], "skysparkMarkerTags": ["ahu", "hvac"], "equipRefs": ["boilerPlantRef", "chillerPlantRef", "coolingTowerPlantRef", "returnAirSystemRef", "ahuPlantRef", "heatPumpPlantRef", "chilledWaterPlantRef", "equipRef", "highTempChilledWaterPlantRef", "hotwaterPlantRef", "lowTempChilledWaterPlantRef", "outsideAirSystemRef", "returnAirPlantRef"]}, {"armsAssetTypeId": 182, "displayName": "Air Comp", "id": "aircomp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "pressure"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["air", "compressor"], "equipRefs": []}, {"armsAssetTypeId": null, "displayName": "Air Curtain", "id": "aircurtain", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [], "skysparkMarkerTags": ["airCurtain", "fan", "hvac"], "equipRefs": []}, {"armsAssetTypeId": 9, "displayName": "Boiler", "id": "boiler", "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwfsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwstsp"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "isolationvalve"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "load"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["boiler", "hvac"], "equipRefs": ["boilerPlantRef", "equipRef"]}, {"armsAssetTypeId": 351, "displayName": "Building Info", "id": "buildinginfo", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "connectorstatus"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "firealarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "oae"}, {"extraSkysparkMarkerTags": [], "sensorId": "oarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "oat"}, {"extraSkysparkMarkerTags": [], "sensorId": "oatwetbulb"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "baseloadhours"}, {"extraSkysparkMarkerTags": [], "sensorId": "firetrip"}, {"extraSkysparkMarkerTags": [], "sensorId": "iaqmsiteacceptableaq<PERSON><PERSON>"}, {"extraSkysparkMarkerTags": [], "sensorId": "iaqmsiteonlinestatus"}, {"extraSkysparkMarkerTags": [], "sensorId": "midnightbaseload"}, {"extraSkysparkMarkerTags": [], "sensorId": "morningbaseload"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancyschedule"}], "skysparkMarkerTags": ["buildingInfo"], "equipRefs": ["weatherStationRef"]}, {"armsAssetTypeId": 349, "displayName": "CHB", "id": "chb", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "chwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "dewpoint"}, {"extraSkysparkMarkerTags": [], "sensorId": "dewpointsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [], "skysparkMarkerTags": ["chilledBeam", "hvac"], "equipRefs": ["<PERSON>u<PERSON><PERSON>", "heatRejectPlantRef", "boilerPlantRef", "chillerPlantRef", "fcuRef", "heatingRef", "highTempChilledWaterPlantRef"]}, {"armsAssetTypeId": 23, "displayName": "Chilled Water Tank", "id": "chilledwatertank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["chilled", "tank", "water"], "equipRefs": ["chillerPlantRef"]}, {"armsAssetTypeId": 5, "displayName": "<PERSON><PERSON>", "id": "chiller", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwrst"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwstsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressordischargetemp"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorspeed"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstatus"}, {"extraSkysparkMarkerTags": [], "sensorId": "condpress"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "efficiency"}, {"extraSkysparkMarkerTags": [], "sensorId": "evappress"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "load"}, {"extraSkysparkMarkerTags": [], "sensorId": "refrigleak"}, {"extraSkysparkMarkerTags": [], "sensorId": "refriglvlair"}, {"extraSkysparkMarkerTags": [], "sensorId": "refriglvlliquid"}, {"extraSkysparkMarkerTags": [], "sensorId": "refrigspair"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "status"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "crt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ert"}, {"extraSkysparkMarkerTags": [], "sensorId": "rms"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergythermal"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalpowerthermal"}], "skysparkMarkerTags": ["chiller", "hvac"], "equipRefs": ["chillerPlantRef", "equipRef", "coolingTowerPlantRef", "scheduleRef"]}, {"armsAssetTypeId": 86, "displayName": "CHW Feed Expansion Tank", "id": "chwfeedexpansiontank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["chilled", "expansion", "tank", "water"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "CHWP", "id": "chwp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "chwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwfsp"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "status"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "rms"}], "skysparkMarkerTags": ["chilled", "hvac", "pump"], "equipRefs": ["chillerPlantRef", "chillerRef", "boilerPlantRef", "boilerRef", "heatRejectPlantRef", "dutyStdbyRef", "equipRef", "coolingTowerPlantRef"]}, {"armsAssetTypeId": null, "displayName": "CoGen", "id": "cogen", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergyactiveexport"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalpoweractiveexport"}, {"extraSkysparkMarkerTags": [], "sensorId": "volumegas"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwrtsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "mode"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalpowerthermal"}], "skysparkMarkerTags": ["elec", "gas", "generator"], "equipRefs": []}, {"armsAssetTypeId": 100, "displayName": "CO Monitoring Unit", "id": "comonitoringunit", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}], "optionalSensors": [], "skysparkMarkerTags": ["air", "co", "monitoringUnit"], "equipRefs": []}, {"armsAssetTypeId": 23, "displayName": "Condenser Water Tank", "id": "condenserwatertank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["condensate", "tank", "water"], "equipRefs": ["coolingTowerPlantRef"]}, {"armsAssetTypeId": 1220, "displayName": "Controller", "id": "controller", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "fault"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}], "skysparkMarkerTags": ["controller"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "CPEF", "id": "cpef", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["exhaust"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["carpark", "exhaust", "fan", "hvac"], "equipRefs": ["coMonitoringUnitRef", "carparkSystemRef", "equipRef"]}, {"armsAssetTypeId": 1, "displayName": "CPSAF", "id": "cpsaf", "mandatorySensors": [{"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["carpark", "fan", "hvac", "supply"], "equipRefs": ["carparkSystemRef", "equipRef"]}, {"armsAssetTypeId": 6, "displayName": "CT", "id": "ct", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "load"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwstsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "diffpressure"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "isolationvalve"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "sumptemp"}], "optionalSensors": [], "skysparkMarkerTags": ["coolingTower", "hvac"], "equipRefs": ["coolingTowerPlantRef", "equipRef", "heatRejectPlantRef"]}, {"armsAssetTypeId": 11, "displayName": "CWP", "id": "cwp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "cwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwfsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "status"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "rms"}], "skysparkMarkerTags": ["condenser", "hvac", "pump"], "equipRefs": ["chillerPlantRef", "chillerRef", "coolingTowerPlantRef", "heatRejectPlantRef", "dutyStdbyRef", "equipRef", "coolingTowerRef"]}, {"armsAssetTypeId": null, "displayName": "Developer Equip", "id": "developerequip", "mandatorySensors": [], "optionalSensors": [], "skysparkMarkerTags": ["developer"], "equipRefs": []}, {"armsAssetTypeId": null, "displayName": "Diesel Tank", "id": "dieseltank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "electricfluidlevel"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "tankvolumediesel"}], "optionalSensors": [], "skysparkMarkerTags": ["diesel", "tank"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "DHWP", "id": "dhwp", "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpsp"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["hot", "hvac", "pump", "domestic"], "equipRefs": ["boilerPlantRef", "boilerRef", "dutyStdbyRef", "equipRef", "domesticPlantRef", "hwPlantRef"]}, {"armsAssetTypeId": 791, "displayName": "Domestic Water Tank", "id": "domesticwatertank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["domestic", "tank", "water"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "EF", "id": "ef", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["exhaust"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ead"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}], "optionalSensors": [], "skysparkMarkerTags": ["exhaust", "fan", "hvac"], "equipRefs": []}, {"armsAssetTypeId": null, "displayName": "Electricity Panel", "id": "elecpanel", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "circuitbreakerfeedback"}, {"extraSkysparkMarkerTags": [], "sensorId": "circuitbreakeroverride"}, {"extraSkysparkMarkerTags": [], "sensorId": "circuitbreakerremote"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}], "optionalSensors": [], "skysparkMarkerTags": ["elec", "panel"], "equipRefs": []}, {"armsAssetTypeId": 17, "displayName": "FCU", "id": "fcu", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "aocct"}, {"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "aohct"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "ead"}, {"extraSkysparkMarkerTags": [], "sensorId": "edhstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "<PERSON><PERSON><PERSON>us"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "fanmode"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "oad"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "raco2"}, {"extraSkysparkMarkerTags": [], "sensorId": "rad"}, {"extraSkysparkMarkerTags": [], "sensorId": "rarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "ratsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": [], "sensorId": "satsp"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}], "skysparkMarkerTags": ["fcu", "hvac"], "equipRefs": ["boilerRef", "heatPumpPlantRef", "chillerPlantRef", "boilerPlantRef", "<PERSON>u<PERSON><PERSON>", "lowTempChilledWaterPlantRef"]}, {"armsAssetTypeId": 86, "displayName": "Feed Expansion Tank", "id": "feedexpansiontank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["expansion", "tank", "water"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "Filtration Pump", "id": "filtrationpump", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "speed"}, {"extraSkysparkMarkerTags": [], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["filtration", "hvac", "pump"], "equipRefs": []}, {"armsAssetTypeId": 1220, "displayName": "Floor Control", "id": "floorcontrol", "mandatorySensors": [{"extraSkysparkMarkerTags": ["floor"], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": [], "sensorId": "co2"}, {"extraSkysparkMarkerTags": ["condenser", "floor"], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "firealarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "firetrip"}, {"extraSkysparkMarkerTags": [], "sensorId": "floorafterhours"}, {"extraSkysparkMarkerTags": [], "sensorId": "floorisolationdamper"}, {"extraSkysparkMarkerTags": [], "sensorId": "flooroccupancy"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": ["condenser", "floor"], "sensorId": "isolationvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "leveldailycomfortpercent"}, {"extraSkysparkMarkerTags": ["common", "floor"], "sensorId": "rad"}, {"extraSkysparkMarkerTags": [], "sensorId": "sad"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "vavenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "zoneafterhours"}, {"extraSkysparkMarkerTags": [], "sensorId": "zoneoccupancy"}, {"extraSkysparkMarkerTags": ["floor"], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": ["floor"], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": ["floor"], "sensorId": "ztspheat"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "avgfloortemp"}], "skysparkMarkerTags": ["floor"], "equipRefs": ["<PERSON>u<PERSON><PERSON>", "equipRef"]}, {"armsAssetTypeId": 1203, "displayName": "Flow Meter", "id": "flowmeter", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "wf"}], "optionalSensors": [], "skysparkMarkerTags": ["flow", "meter"], "equipRefs": []}, {"armsAssetTypeId": null, "displayName": "FTU", "id": "ftu", "mandatorySensors": [{"extraSkysparkMarkerTags": ["fan"], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "flowsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "maxflowsp"}, {"extraSkysparkMarkerTags": ["air"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "vcdfeedback"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [], "skysparkMarkerTags": ["ftu", "hvac"], "equipRefs": ["<PERSON>u<PERSON><PERSON>", "boilerPlantRef"]}, {"armsAssetTypeId": 1, "displayName": "GEF", "id": "gef", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["exhaust"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ead"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["exhaust", "fan", "general", "hvac"], "equipRefs": []}, {"armsAssetTypeId": null, "displayName": "Electricity Generator", "id": "generator", "mandatorySensors": [{"extraSkysparkMarkerTags": ["generator"], "sensorId": "batteryvoltage"}, {"extraSkysparkMarkerTags": ["engine", "generator"], "sensorId": "coolanttemp"}, {"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}, {"extraSkysparkMarkerTags": ["engine", "generator"], "sensorId": "fuellevel"}, {"extraSkysparkMarkerTags": ["engine", "generator"], "sensorId": "oilpressure"}, {"extraSkysparkMarkerTags": ["engine", "generator"], "sensorId": "speed"}], "optionalSensors": [], "skysparkMarkerTags": ["elec", "generator"], "equipRefs": []}, {"armsAssetTypeId": 1202, "displayName": "GM", "id": "gm", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "volumegas"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "volumegasraw"}], "skysparkMarkerTags": ["gas", "meter"], "equipRefs": ["virtualMeterRef"]}, {"armsAssetTypeId": null, "displayName": "Heatpump", "id": "heatpump", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwstsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressordischargetemp"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorspeed"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstatus"}, {"extraSkysparkMarkerTags": [], "sensorId": "dp"}, {"extraSkysparkMarkerTags": [], "sensorId": "dpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "efficiency"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwstsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "isolationvalvecool"}, {"extraSkysparkMarkerTags": [], "sensorId": "isolationvalveheat"}, {"extraSkysparkMarkerTags": [], "sensorId": "load"}, {"extraSkysparkMarkerTags": [], "sensorId": "refrigleak"}, {"extraSkysparkMarkerTags": [], "sensorId": "refriglvlair"}, {"extraSkysparkMarkerTags": [], "sensorId": "refriglvlliquid"}, {"extraSkysparkMarkerTags": [], "sensorId": "refrigspair"}, {"extraSkysparkMarkerTags": [], "sensorId": "returntemp"}, {"extraSkysparkMarkerTags": [], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "supplytemp"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "heatenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "rms"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergythermal"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalpowerthermal"}], "skysparkMarkerTags": ["heatPump", "hvac"], "equipRefs": ["heatPumpPlantRef"]}, {"armsAssetTypeId": 12, "displayName": "HEX", "id": "hex", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "isolationvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "pdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "phwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "phwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "phwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "sdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "shwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "shwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "shwv"}], "optionalSensors": [], "skysparkMarkerTags": ["heatExchanger", "hvac"], "equipRefs": ["chillerPlantRef", "coolingTowerPlantRef", "equipRef", "heatRejectPlantRef"]}, {"armsAssetTypeId": 23, "displayName": "Hot Water Tank", "id": "hotwatertank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["hot", "tank", "water"], "equipRefs": ["boilerPlantRef"]}, {"armsAssetTypeId": 204, "displayName": "HWC", "id": "hwc", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "satsp"}], "optionalSensors": [], "skysparkMarkerTags": ["coil", "hot", "hvac", "water"], "equipRefs": ["boilerPlantRef"]}, {"armsAssetTypeId": 86, "displayName": "HW Feed Expansion Tank", "id": "hwfeedexpansiontank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["expansion", "hot", "tank", "water"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "HWP", "id": "hwp", "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpsp"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["hot", "hvac", "pump"], "equipRefs": ["boilerPlantRef", "boilerRef", "dutyStdbyRef", "equipRef", "domesticPlantRef", "hwPlantRef"]}, {"armsAssetTypeId": null, "displayName": "iAQM", "id": "iaqm", "mandatorySensors": [{"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "accel"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "lightlevel"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "noise"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "particulatematter10"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "particulatematter2p5"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "rssi"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "voc"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": ["iaqm"], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["iaqm"], "equipRefs": []}, {"armsAssetTypeId": 41, "displayName": "IU", "id": "iu", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "chwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "terminalload"}], "optionalSensors": [], "skysparkMarkerTags": ["hvac", "induction", "iu"], "equipRefs": ["<PERSON>u<PERSON><PERSON>"]}, {"armsAssetTypeId": 1, "displayName": "KEF", "id": "kef", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["exhaust"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["exhaust", "fan", "hvac", "kitchen"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "KMF", "id": "kmf", "mandatorySensors": [{"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "kitchen", "makeup<PERSON>an"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "KSAF", "id": "ksaf", "mandatorySensors": [{"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "kitchen", "supply"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "LDEF", "id": "ldef", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["exhaust"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["exhaust", "fan", "hvac", "loadingDock"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "LDSAF", "id": "ldsaf", "mandatorySensors": [{"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "loadingDock", "supply"], "equipRefs": []}, {"armsAssetTypeId": null, "displayName": "Lift", "id": "lift", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["lift"], "equipRefs": []}, {"armsAssetTypeId": null, "displayName": "Lifts Group", "id": "liftsgroup", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["liftsGroup"], "equipRefs": []}, {"armsAssetTypeId": 1228, "displayName": "Lights Group", "id": "lightsgroup", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "current"}, {"extraSkysparkMarkerTags": ["lights"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "lightlevel"}, {"extraSkysparkMarkerTags": [], "sensorId": "lightsstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "lightsstatus"}, {"extraSkysparkMarkerTags": ["lights"], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancyindicator"}, {"extraSkysparkMarkerTags": ["lights"], "sensorId": "override"}], "optionalSensors": [], "skysparkMarkerTags": ["lightsGroup"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "LMREF", "id": "lmref", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["exhaust"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}], "optionalSensors": [], "skysparkMarkerTags": ["exhaust", "fan", "hvac", "liftMotorRoom"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "LMRSAF", "id": "lmrsaf", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "liftMotorRoom", "supply"], "equipRefs": []}, {"armsAssetTypeId": 1202, "displayName": "Main Gas Meter", "id": "maingasmeter", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "volumegas"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "volumegasraw"}], "skysparkMarkerTags": ["gas", "meter", "siteMeter"], "equipRefs": []}, {"armsAssetTypeId": 1203, "displayName": "Main Water Meter", "id": "mainwatermeter", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "volumewater"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "volumewaterraw"}], "skysparkMarkerTags": ["meter", "siteMeter", "water"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "MCHWP", "id": "mchwp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "chwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwfsp"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["chilled", "hvac", "makeupPump", "pump"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "MHWP", "id": "mhwp", "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpsp"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["hot", "hvac", "makeupPump", "pump"], "equipRefs": []}, {"armsAssetTypeId": 223, "displayName": "Mixing Box", "id": "mixingbox", "mandatorySensors": [{"extraSkysparkMarkerTags": ["cmd"], "sensorId": "damper"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [], "skysparkMarkerTags": ["hvac", "mixingBox"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "MUF", "id": "muf", "mandatorySensors": [{"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "fanmode"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "makeup<PERSON>an"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "OAF", "id": "oaf", "mandatorySensors": [{"extraSkysparkMarkerTags": ["fan", "outside"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["return"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "oarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "oasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "oaspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "oat"}, {"extraSkysparkMarkerTags": [], "sensorId": "oad"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": ["fan", "outside"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "outside"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["fan", "outside"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "outside"], "sensorId": "status"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}], "skysparkMarkerTags": ["fan", "hvac", "outside"], "equipRefs": []}, {"armsAssetTypeId": 209, "displayName": "PAC", "id": "pac", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "aocct"}, {"extraSkysparkMarkerTags": [], "sensorId": "aohct"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorload"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstatus"}, {"extraSkysparkMarkerTags": [], "sensorId": "coolingcall"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwcall"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "ead"}, {"extraSkysparkMarkerTags": [], "sensorId": "economymode"}, {"extraSkysparkMarkerTags": [], "sensorId": "edhstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "<PERSON><PERSON><PERSON>us"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "load"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterstatus"}, {"extraSkysparkMarkerTags": [], "sensorId": "heatingcall"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "mat"}, {"extraSkysparkMarkerTags": [], "sensorId": "moad"}, {"extraSkysparkMarkerTags": [], "sensorId": "oad"}, {"extraSkysparkMarkerTags": [], "sensorId": "oat"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "override"}, {"extraSkysparkMarkerTags": [], "sensorId": "raco2"}, {"extraSkysparkMarkerTags": [], "sensorId": "rad"}, {"extraSkysparkMarkerTags": [], "sensorId": "rarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "ratsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "reversevalvestartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": [], "sensorId": "satsp"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "mode"}, {"extraSkysparkMarkerTags": [], "sensorId": "oae"}, {"extraSkysparkMarkerTags": [], "sensorId": "oarh"}], "skysparkMarkerTags": ["hvac", "pac"], "equipRefs": ["coolingTowerPlantRef", "<PERSON>u<PERSON><PERSON>", "boilerPlantRef", "chillerPlantRef", "heatRejectPlantRef", "returnAirSystemRef"]}, {"armsAssetTypeId": 11, "displayName": "PCWP", "id": "pcwp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "cwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwfsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["condenser", "hvac", "primary", "pump"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "PHWP", "id": "phwp", "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpsp"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["hot", "hvac", "primary", "pump"], "equipRefs": []}, {"armsAssetTypeId": 909, "displayName": "Plant - Boiler", "id": "plantboiler", "isPlant": true, "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "bypassflow"}, {"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwrtcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwstcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwstcommonsp"}, {"extraSkysparkMarkerTags": ["common", "hot"], "sensorId": "load"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "lot"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "hwrtabp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwrtbbp"}], "skysparkMarkerTags": ["boilerPlant", "hvac", "virtual"], "equipRefs": ["boilerPlantRef"]}, {"armsAssetTypeId": 910, "displayName": "Plant - <PERSON>ller", "id": "<PERSON><PERSON><PERSON>", "isPlant": true, "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "bypasschwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "bypassflow"}, {"extraSkysparkMarkerTags": [], "sensorId": "bypassflowsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "bypasspressure"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "injectionvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwdpcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwdpcommonsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwrtcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwstcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwstcommonsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "efficiency"}, {"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "isolationvalve"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "load"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "lot"}, {"extraSkysparkMarkerTags": ["run"], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "refrigleak"}, {"extraSkysparkMarkerTags": [], "sensorId": "refriglvlair"}, {"extraSkysparkMarkerTags": [], "sensorId": "refrigspair"}, {"extraSkysparkMarkerTags": [], "sensorId": "stage"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "totalenergythermal"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalpowerthermal"}], "skysparkMarkerTags": ["chillerPlant", "hvac", "virtual"], "equipRefs": ["coolingTowerPlantRef", "equipRef"]}, {"armsAssetTypeId": 908, "displayName": "Plant - Cooling Tower", "id": "plantcoolingtower", "isPlant": true, "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "diffpressure"}, {"extraSkysparkMarkerTags": [], "sensorId": "oatwetbulb"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpcommonsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwfcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwrtcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwstabp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwstbbp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwstcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwstcommonsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["common", "condenser"], "sensorId": "load"}], "optionalSensors": [], "skysparkMarkerTags": ["coolingTowerPlant", "hvac", "virtual"], "equipRefs": []}, {"armsAssetTypeId": null, "displayName": "Plant - Heatpump", "id": "plantheatpump", "isPlant": true, "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwdpcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwrtcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwstcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwstcommonsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "commonretpresscool"}, {"extraSkysparkMarkerTags": [], "sensorId": "commonretpressheat"}, {"extraSkysparkMarkerTags": [], "sensorId": "commonsuppresscool"}, {"extraSkysparkMarkerTags": [], "sensorId": "commonsuppressheat"}, {"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwrtcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwstcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwstcommonsp"}, {"extraSkysparkMarkerTags": ["common"], "sensorId": "load"}], "optionalSensors": [], "skysparkMarkerTags": ["heatPumpPlant", "hvac", "virtual"], "equipRefs": []}, {"armsAssetTypeId": 908, "displayName": "Plant - Heat Rejection", "id": "plantheatrejection", "isPlant": true, "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwdpcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpcommonsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "supplytempsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "systemdp"}], "optionalSensors": [], "skysparkMarkerTags": ["heatRejectPlant", "hvac", "virtual"], "equipRefs": ["equipRef"]}, {"armsAssetTypeId": null, "displayName": "Plant - HWP", "id": "planthotwaterpump", "isPlant": true, "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "hwdpcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwstcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwrtcommon"}], "optionalSensors": [], "skysparkMarkerTags": ["pumpPlant", "hvac", "virtual"], "equipRefs": []}, {"armsAssetTypeId": 908, "displayName": "Plant - Tenant Cooling Tower", "id": "planttenantcoolingtower", "isPlant": true, "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpcommonsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwrtcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwstcommon"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwstcommonsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}], "optionalSensors": [], "skysparkMarkerTags": ["coolingTowerPlant", "hvac", "tenant", "virtual"], "equipRefs": []}, {"armsAssetTypeId": 971, "displayName": "PM", "id": "pm", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "current"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergyactiveexport"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergyactiveimport"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalpoweractiveexport"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalpoweractiveimport"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "acfrequency"}, {"extraSkysparkMarkerTags": [], "sensorId": "apparentpowerexport"}, {"extraSkysparkMarkerTags": [], "sensorId": "apparentpowerimport"}, {"extraSkysparkMarkerTags": [], "sensorId": "pf"}, {"extraSkysparkMarkerTags": [], "sensorId": "reactivepowerexport"}, {"extraSkysparkMarkerTags": [], "sensorId": "reactivepowerimport"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergyactiveexportraw"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergyactiveimportraw"}, {"extraSkysparkMarkerTags": [], "sensorId": "voltage"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergyapparentimport"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergyreactiveimport"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergyapparentexport"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalenergyreactiveexport"}], "skysparkMarkerTags": ["elec", "meter"], "equipRefs": ["gegConsumptionTariffHisRef", "tariffHisRef", "switchboardMeterReference", "ghgSourceRef", "optimaReference", "solarControlPanelRef", "virtualMeterRef"]}, {"armsAssetTypeId": null, "displayName": "Pool", "id": "pool", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "cwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "watertemp"}, {"extraSkysparkMarkerTags": [], "sensorId": "watertempsp"}], "optionalSensors": [], "skysparkMarkerTags": ["pool", "water"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "PTCWP", "id": "ptcwp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "cwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwfsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["condenser", "hvac", "primary", "pump", "tenant"], "equipRefs": ["coolingTowerPlantRef"]}, {"armsAssetTypeId": 11, "displayName": "PTHWP", "id": "pthwp", "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpsp"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["hot", "hvac", "primary", "pump", "tenant"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "Pump", "id": "pump", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "dp"}, {"extraSkysparkMarkerTags": [], "sensorId": "dpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "speed"}, {"extraSkysparkMarkerTags": [], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": [], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "wf"}, {"extraSkysparkMarkerTags": [], "sensorId": "wfsp"}], "optionalSensors": [], "skysparkMarkerTags": ["hvac", "pump"], "equipRefs": ["coolingTowerPlantRef", "heatRejectPlantRef", "boilerPlantRef", "heatPumpPlantRef", "chillerRef", "equipRef"]}, {"armsAssetTypeId": 1, "displayName": "RAF", "id": "raf", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "dewpoint"}, {"extraSkysparkMarkerTags": [], "sensorId": "dewpointsp"}, {"extraSkysparkMarkerTags": ["fan", "return"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["return"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "raco2"}, {"extraSkysparkMarkerTags": [], "sensorId": "rae"}, {"extraSkysparkMarkerTags": [], "sensorId": "rarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "rarp"}, {"extraSkysparkMarkerTags": [], "sensorId": "rasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "raspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": ["fan", "return"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "return"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["fan", "return"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "return"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "return"], "equipRefs": ["<PERSON>u<PERSON><PERSON>", "ahuPlantRef", "equipRef", "returnAirPlantRef"]}, {"armsAssetTypeId": 814, "displayName": "Rain Water Tank", "id": "rainwatertank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "electricfluidlevel"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "fillingpercentage"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}, {"extraSkysparkMarkerTags": [], "sensorId": "rwtwaterusage"}, {"extraSkysparkMarkerTags": [], "sensorId": "tankvolume"}], "optionalSensors": [], "skysparkMarkerTags": ["rain", "tank", "water"], "equipRefs": []}, {"armsAssetTypeId": 811, "displayName": "Rainwater Tank Pump", "id": "rainwatertankpump", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "current"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "dp"}, {"extraSkysparkMarkerTags": [], "sensorId": "inputpressure"}, {"extraSkysparkMarkerTags": [], "sensorId": "outputpressure"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["pump", "rain", "water"], "equipRefs": ["rainwaterTankRef"]}, {"armsAssetTypeId": 1, "displayName": "RLFAF", "id": "rlfaf", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["exhaust"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "relief"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "SAF", "id": "saf", "mandatorySensors": [{"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": [], "sensorId": "satsp"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "rms"}], "skysparkMarkerTags": ["fan", "hvac", "supply"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "SCHWP", "id": "schwp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "chwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwfsp"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["chilled"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["chilled", "hvac", "pump", "secondary"], "equipRefs": ["chillerPlantRef"]}, {"armsAssetTypeId": 11, "displayName": "SCWP", "id": "scwp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "cwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwfsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["condenser", "hvac", "pump", "secondary"], "equipRefs": ["heatRejectPlantRef"]}, {"armsAssetTypeId": 1, "displayName": "SEF", "id": "sef", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["exhaust"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["exhaust", "fan", "hvac", "smoke"], "equipRefs": []}, {"armsAssetTypeId": 86, "displayName": "SHW Feed Expansion Tank", "id": "shwfeedexpansiontank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["expansion", "hot", "secondary", "tank", "water"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "SHWP", "id": "shwp", "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpsp"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["hot", "hvac", "pump", "secondary"], "equipRefs": []}, {"armsAssetTypeId": 996, "displayName": "Solar Inverter", "id": "solarinverter", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "invertertemp"}, {"extraSkysparkMarkerTags": [], "sensorId": "mode"}], "optionalSensors": [], "skysparkMarkerTags": ["inverter", "solar"], "equipRefs": ["sicRef"]}, {"armsAssetTypeId": 913, "displayName": "Solar Control Panel", "id": "solarinvertercontroller", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "predictedpowerexport"}], "optionalSensors": [], "skysparkMarkerTags": ["controller", "solar"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "SPF", "id": "spf", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "stairPressure"], "equipRefs": []}, {"armsAssetTypeId": 194, "displayName": "Split Cassette", "id": "splitcassette", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "compressorstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstatus"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "mat"}, {"extraSkysparkMarkerTags": [], "sensorId": "moad"}, {"extraSkysparkMarkerTags": [], "sensorId": "moadfeedback"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "raco2"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "reversevalvestartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}], "skysparkMarkerTags": ["cassette", "hvac", "split"], "equipRefs": []}, {"armsAssetTypeId": 195, "displayName": "Split Ceiling", "id": "splitceiling", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "compressorstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstatus"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "mat"}, {"extraSkysparkMarkerTags": [], "sensorId": "moad"}, {"extraSkysparkMarkerTags": [], "sensorId": "moadfeedback"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "raco2"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "reversevalvestartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}], "skysparkMarkerTags": ["ceiling", "hvac", "split"], "equipRefs": []}, {"armsAssetTypeId": 163, "displayName": "Split Ducted", "id": "splitducted", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstatus"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "enable"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterstatus"}, {"extraSkysparkMarkerTags": [], "sensorId": "mat"}, {"extraSkysparkMarkerTags": [], "sensorId": "moad"}, {"extraSkysparkMarkerTags": [], "sensorId": "moadfeedback"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "raco2"}, {"extraSkysparkMarkerTags": ["common", "floor"], "sensorId": "rad"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "reversevalvestartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}], "skysparkMarkerTags": ["ducted", "hvac", "split"], "equipRefs": []}, {"armsAssetTypeId": 197, "displayName": "Split Wall", "id": "splitwall", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "compressorstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstatus"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "mat"}, {"extraSkysparkMarkerTags": [], "sensorId": "moad"}, {"extraSkysparkMarkerTags": [], "sensorId": "moadfeedback"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "raco2"}, {"extraSkysparkMarkerTags": [], "sensorId": "reversevalvestartstop"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}], "skysparkMarkerTags": ["hvac", "split", "wallMount"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "SREF", "id": "sref", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}], "optionalSensors": [], "skysparkMarkerTags": ["exhaust", "fan", "hvac", "serverRoom"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "SSF", "id": "ssf", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "smokeSpill"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "STCWP", "id": "stcwp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "cwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwfsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["condenser", "hvac", "pump", "secondary", "tenant"], "equipRefs": ["coolingTowerPlantRef", "dutyStdbyRef"]}, {"armsAssetTypeId": 11, "displayName": "STHWP", "id": "sthwp", "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpsp"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["hot", "hvac", "pump", "secondary", "tenant"], "equipRefs": []}, {"armsAssetTypeId": 841, "displayName": "Storm Water Tank", "id": "stormwatertank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["storm", "tank", "water"], "equipRefs": []}, {"armsAssetTypeId": 11, "displayName": "<PERSON><PERSON>", "id": "sumppump", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "speed"}, {"extraSkysparkMarkerTags": [], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["hvac", "pump", "sump"], "equipRefs": []}, {"armsAssetTypeId": 6, "displayName": "TCT", "id": "tct", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwstsp"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "isolationvalve"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["condenser", "fan"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["coolingTower", "hvac", "tenant"], "equipRefs": ["coolingTowerPlantRef", "equipRef", "heatRejectPlantRef"]}, {"armsAssetTypeId": 11, "displayName": "TCWP", "id": "tcwp", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "cwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwdpsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwf"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwfsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": ["water"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "speedfeedback"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["condenser"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["condenser", "hvac", "pump", "tenant"], "equipRefs": ["coolingTowerPlantRef", "dutyStdbyRef", "equipRef", "heatRejectPlantRef"]}, {"armsAssetTypeId": 1, "displayName": "TEF", "id": "tef", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co"}, {"extraSkysparkMarkerTags": ["exhaust"], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "cosp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easp"}, {"extraSkysparkMarkerTags": [], "sensorId": "easpsp"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "pir"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["exhaust", "fan"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["exhaust", "fan", "hvac", "toilet"], "equipRefs": ["<PERSON>u<PERSON><PERSON>", "boilerPlantRef"]}, {"armsAssetTypeId": 1234, "displayName": "Thermal Meter", "id": "thermalmeter", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "totalenergythermal"}, {"extraSkysparkMarkerTags": [], "sensorId": "totalpowerthermal"}], "optionalSensors": [], "skysparkMarkerTags": ["elec", "meter", "thermal"], "equipRefs": ["virtualMeterRef"]}, {"armsAssetTypeId": 11, "displayName": "THWP", "id": "thwp", "mandatorySensors": [{"extraSkysparkMarkerTags": ["hot"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwdpsp"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["hot"], "sensorId": "status"}], "optionalSensors": [], "skysparkMarkerTags": ["hot", "hvac", "pump", "tenant"], "equipRefs": []}, {"armsAssetTypeId": 1, "displayName": "TSAF", "id": "tsaf", "mandatorySensors": [{"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "filterdp"}, {"extraSkysparkMarkerTags": ["supply"], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "sarh"}, {"extraSkysparkMarkerTags": [], "sensorId": "sasp"}, {"extraSkysparkMarkerTags": [], "sensorId": "saspsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "speed"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "status"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}], "optionalSensors": [], "skysparkMarkerTags": ["fan", "hvac", "supply", "toilet"], "equipRefs": []}, {"armsAssetTypeId": null, "displayName": "UV Lights Group", "id": "uvlightsgroup", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "current"}, {"extraSkysparkMarkerTags": ["uv"], "sensorId": "lightsstatus"}], "optionalSensors": [], "skysparkMarkerTags": ["lightsGroup", "uv"], "equipRefs": ["rainwaterTankRef"]}, {"armsAssetTypeId": 2, "displayName": "VAV", "id": "vav", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "damper"}, {"extraSkysparkMarkerTags": [], "sensorId": "dampercmd"}, {"extraSkysparkMarkerTags": [], "sensorId": "edhstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "<PERSON><PERSON><PERSON>us"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "flowair"}, {"extraSkysparkMarkerTags": [], "sensorId": "flowsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "maxflowsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "minflow"}, {"extraSkysparkMarkerTags": ["air"], "sensorId": "minflowsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "mode"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}, {"extraSkysparkMarkerTags": [], "sensorId": "satsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "terminalload"}, {"extraSkysparkMarkerTags": [], "sensorId": "vcd"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "hwv"}, {"extraSkysparkMarkerTags": ["fan", "supply"], "sensorId": "startstop"}], "skysparkMarkerTags": ["hvac", "vav"], "equipRefs": ["<PERSON>u<PERSON><PERSON>", "boilerPlantRef", "equipRef"]}, {"armsAssetTypeId": null, "displayName": "<PERSON><PERSON><PERSON>", "id": "vthub", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "accel"}, {"extraSkysparkMarkerTags": [], "sensorId": "batteryvoltage"}, {"extraSkysparkMarkerTags": [], "sensorId": "pcbtemp"}, {"extraSkysparkMarkerTags": [], "sensorId": "vthubppv"}], "optionalSensors": [], "skysparkMarkerTags": ["vthub"], "equipRefs": ["rainwaterTankRef"]}, {"armsAssetTypeId": 23, "displayName": "Water Tank", "id": "watertank", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "level"}], "optionalSensors": [], "skysparkMarkerTags": ["tank", "water"], "equipRefs": []}, {"armsAssetTypeId": 1203, "displayName": "WM", "id": "wm", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "volumewater"}], "optionalSensors": [{"extraSkysparkMarkerTags": [], "sensorId": "volumewaterraw"}], "skysparkMarkerTags": ["meter", "water"], "equipRefs": ["virtualMeterRef"]}, {"armsAssetTypeId": 1237, "displayName": "Zone", "id": "zone", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "alarm"}, {"extraSkysparkMarkerTags": [], "sensorId": "co2"}, {"extraSkysparkMarkerTags": [], "sensorId": "dampercmd"}, {"extraSkysparkMarkerTags": [], "sensorId": "edhstartstop"}, {"extraSkysparkMarkerTags": [], "sensorId": "<PERSON><PERSON><PERSON>us"}, {"extraSkysparkMarkerTags": [], "sensorId": "equipdailycomfortpercent"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "zrh"}, {"extraSkysparkMarkerTags": [], "sensorId": "zt"}, {"extraSkysparkMarkerTags": [], "sensorId": "occupancy"}, {"extraSkysparkMarkerTags": [], "sensorId": "ahenable"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztsp"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspcool"}, {"extraSkysparkMarkerTags": [], "sensorId": "ztspheat"}], "optionalSensors": [], "skysparkMarkerTags": ["hvac", "zone"], "equipRefs": ["<PERSON>u<PERSON><PERSON>"]}, {"armsAssetTypeId": 12, "displayName": "CHW HEX", "id": "chwhex", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "pdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "pchwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "pchwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "pchwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "sdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "schwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "schwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "schwv"}], "optionalSensors": [], "skysparkMarkerTags": ["heatExchanger", "hvac", "chilled"], "equipRefs": []}, {"armsAssetTypeId": 12, "displayName": "CW HEX", "id": "cwhex", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "bypassvalve"}, {"extraSkysparkMarkerTags": [], "sensorId": "fault"}, {"extraSkysparkMarkerTags": [], "sensorId": "pdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "pcwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "pcwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "pcwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "sdp"}, {"extraSkysparkMarkerTags": [], "sensorId": "scwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "scwst"}, {"extraSkysparkMarkerTags": [], "sensorId": "scwv"}], "optionalSensors": [], "skysparkMarkerTags": ["heatExchanger", "hvac", "condensor"], "equipRefs": []}, {"armsAssetTypeId": 201, "displayName": "CRAC CHW", "id": "cracchw", "mandatorySensors": [{"extraSkysparkMarkerTags": [], "sensorId": "compressorspeed"}, {"extraSkysparkMarkerTags": [], "sensorId": "compressorstatus"}, {"extraSkysparkMarkerTags": [], "sensorId": "freecooling"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "inverterspeed"}, {"extraSkysparkMarkerTags": [], "sensorId": "chwt"}, {"extraSkysparkMarkerTags": [], "sensorId": "speed"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwv"}, {"extraSkysparkMarkerTags": [], "sensorId": "cwrt"}, {"extraSkysparkMarkerTags": [], "sensorId": "rat"}, {"extraSkysparkMarkerTags": [], "sensorId": "sat"}], "optionalSensors": [], "skysparkMarkerTags": ["crac", "hvac", "chilled", "water"], "equipRefs": []}]